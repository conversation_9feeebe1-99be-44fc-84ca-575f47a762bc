import { convexAdapter } from "@convex-dev/better-auth";
import { convex, crossDomain } from "@convex-dev/better-auth/plugins";
import { admin, magicLink, openAPI, twoFactor, username, organization } from "better-auth/plugins";
import { passkey } from "better-auth/plugins/passkey";
import { betterAuth } from "better-auth";
import { betterAuthComponent } from "../convex/auth";
import { requireMutationCtx } from "@convex-dev/better-auth/utils";
import type { GenericCtx } from "../convex/_generated/server";
import { config } from "@workspace/config";
import { sendEmail } from "@workspace/mail";
import { getBaseUrl } from "@workspace/utils";
import { invitationOnly } from "../convex/auth/plugins/invitationOnly";

export const createAuth = (ctx: GenericCtx) => betterAuth({
  baseURL: getBaseUrl(),
  trustedOrigins: [getBaseUrl()],
  appName: config.appName,
  database: convexAdapter(ctx, betterAuthComponent),
  
  user: {
    additionalFields: {
      onboardingComplete: {
        type: "boolean",
        required: false,
      },
      role: {
        type: "string",
        required: false,
      },
    },
  },

  emailAndPassword: {
    enabled: true,
    requireEmailVerification: config.auth.enableSignup,
    autoSignIn: !config.auth.enableSignup,
    sendResetPassword: async ({ user, url }) => {
      await sendEmail({
        to: user.email,
        templateId: "forgotPassword",
        context: {
          url,
          name: user.name,
        },
      });
    },
  },

  account: {
    accountLinking: {
      enabled: true,
      trustedProviders: ["google", "github"],
    },
  },

  socialProviders: {
    google: {
      clientId: process.env.GOOGLE_CLIENT_ID as string,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET as string,
      scope: ["email", "profile"],
    },
    github: {
      clientId: process.env.GITHUB_CLIENT_ID as string,
      clientSecret: process.env.GITHUB_CLIENT_SECRET as string,
      scope: ["user:email"],
    },
  },

  session: {
    expiresIn: config.auth.sessionCookieMaxAge,
    freshAge: 0,
  },

  plugins: [
    convex(),
    crossDomain({
      siteUrl: getBaseUrl(),
    }),
    admin(),
    username(),
    passkey(),
    magicLink({
      disableSignUp: true,
      sendMagicLink: async ({ email, url }) => {
        await sendEmail({
          to: email,
          templateId: "magicLink",
          context: {
            url,
          },
        });
      },
    }),
    organization({
      sendInvitationEmail: async ({ email, id, organization }) => {
        try {
          const url = new URL("/auth/signup", getBaseUrl());
          url.searchParams.set("invitationId", id);
          url.searchParams.set("email", email);

          await sendEmail({
            to: email,
            templateId: "organizationInvitation",
            context: {
              organizationName: organization.name,
              url: url.toString(),
            },
          });
        } catch (error) {
          console.error("Error sending invitation email:", error);
        }
      },
    }),
    openAPI(),
    invitationOnly(),
    twoFactor(),
  ],
});
