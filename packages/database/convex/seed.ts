import { Polar } from "@polar-sh/sdk";
import { internalAction } from "./_generated/server";

const accessToken = process.env.POLAR_ACCESS_TOKEN!;

const polar = new Polar({
  accessToken,
  server: "sandbox",
});

export const FREE_PLAN_NAME = "Free Plan";
export const PREMIUM_MONTHLY_PLAN_NAME = "Premium Monthly";
export const PREMIUM_YEARLY_PLAN_NAME = "Premium Yearly";
export const PREMIUM_PLUS_MONTHLY_PLAN_NAME = "Premium Plus Monthly";
export const PREMIUM_PLUS_YEARLY_PLAN_NAME = "Premium Plus Yearly";

const seed = internalAction({
  handler: async (ctx) => {
    // Validate environment variables
    if (!accessToken) {
      throw new Error("POLAR_ORGANIZATION_TOKEN environment variable is required");
    }

    // Check if products already exist
    const result = await polar.products.list({
      isArchived: false,
      limit: 1,
    });
    
    let hasProducts = false;
    try {
      for await (const page of result) {
        if (page.result?.items && page.result.items.length > 0) {
          hasProducts = true;
          break;
        }
      }
    } catch (error) {
      console.error("Error checking existing products:", error);
      // Continue with seeding if we can't check
      hasProducts = false;
    }

    // Return early if the Polar organization already has products, ensures
    // this doesn't run more than once.
    if (hasProducts) {
      console.log("Products already exist");
      return;
    }

    // Create example products. In a real app you would likely create your
    // products in the Polar dashboard and reference them by id in your application.
    await Promise.all([
      polar.products.create({
        name: FREE_PLAN_NAME,
        description: "Free plan for testing purposes.",
        recurringInterval: "month",
        prices: [
          {
            amountType: "free",
          },
        ],
      }),
      polar.products.create({
        name: PREMIUM_MONTHLY_PLAN_NAME,
        description: "All premium features for one low monthly price.",
        recurringInterval: "month",
        prices: [
          {
            amountType: "fixed",
            priceAmount: 1000, // $10.00
            priceCurrency: "usd",
          },
        ],
      }),
      polar.products.create({
        name: PREMIUM_YEARLY_PLAN_NAME,
        description: "All premium features for one low annual price (2 months free).",
        recurringInterval: "year",
        prices: [
          {
            amountType: "fixed",
            priceAmount: 10000, // $100.00 (vs $120 monthly)
            priceCurrency: "usd",
          },
        ],
      }),
      polar.products.create({
        name: PREMIUM_PLUS_MONTHLY_PLAN_NAME,
        description: "Premium features plus advanced tools for monthly subscribers.",
        recurringInterval: "month",
        prices: [
          {
            amountType: "fixed",  
            priceAmount: 2000, // $20.00
            priceCurrency: "usd",
          },
        ],
      }),
      polar.products.create({
        name: PREMIUM_PLUS_YEARLY_PLAN_NAME,
        description: "Premium features plus advanced tools for annual subscribers (2 months free).",
        recurringInterval: "year",
        prices: [
          {
            amountType: "fixed",
            priceAmount: 20000, // $200.00 (vs $240 monthly)
            priceCurrency: "usd",
          },
        ],
      }),
    ]);
  },
});

export default seed;