import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";
import { AiChatRole, PurchaseType } from "./helpers/enums";

export default defineSchema({
  users: defineTable({
    name: v.string(),
    email: v.string(),
    emailVerified: v.boolean(),
    image: v.optional(v.string()),
    username: v.optional(v.string()),
    role: v.optional(v.string()),
    banned: v.optional(v.boolean()),
    banReason: v.optional(v.string()),
    banExpires: v.optional(v.number()),
    onboardingComplete: v.boolean(),
    paymentsCustomerId: v.optional(v.string()),
    twoFactorEnabled: v.optional(v.boolean()),
  })
    .index("by_email", ["email"])
    .index("by_username", ["username"]),

  sessions: defineTable({
    expiresAt: v.number(),
    ipAddress: v.optional(v.string()),
    userAgent: v.optional(v.string()),
    userId: v.id("users"),
    impersonatedBy: v.optional(v.string()),
    activeOrganizationId: v.optional(v.string()),
    token: v.string(),
  }).index("by_token", ["token"]),

  accounts: defineTable({
    accountId: v.string(),
    providerId: v.string(),
    userId: v.id("users"),
    accessToken: v.optional(v.string()),
    refreshToken: v.optional(v.string()),
    idToken: v.optional(v.string()),
    expiresAt: v.optional(v.number()),
    password: v.optional(v.string()),
    accessTokenExpiresAt: v.optional(v.number()),
    refreshTokenExpiresAt: v.optional(v.number()),
    scope: v.optional(v.string()),
  }).index("by_userId", ["userId"]),

  verifications: defineTable({
    identifier: v.string(),
    value: v.string(),
    expiresAt: v.number(),
  }),

  passkeys: defineTable({
    name: v.optional(v.string()),
    publicKey: v.string(),
    userId: v.id("users"),
    credentialID: v.string(),
    counter: v.number(),
    deviceType: v.string(),
    backedUp: v.boolean(),
    transports: v.optional(v.string()),
  }).index("by_userId", ["userId"]),

  twoFactors: defineTable({
    secret: v.string(),
    backupCodes: v.string(),
    userId: v.id("users"),
  }).index("by_userId", ["userId"]),

  organizations: defineTable({
    name: v.string(),
    slug: v.optional(v.string()),
    logo: v.optional(v.string()),
    metadata: v.optional(v.string()),
    paymentsCustomerId: v.optional(v.string()),
  }).index("by_slug", ["slug"]),

  members: defineTable({
    organizationId: v.id("organizations"),
    userId: v.id("users"),
    role: v.string(),
  }).index("by_organizationId_userId", ["organizationId", "userId"]),

  invitations: defineTable({
    organizationId: v.id("organizations"),
    email: v.string(),
    role: v.optional(v.string()),
    status: v.string(),
    expiresAt: v.number(),
    inviterId: v.id("users"),
  }).index("by_organizationId", ["organizationId"])
    .index("by_email_and_status", ["email", "status"]),

  purchases: defineTable({
    organizationId: v.optional(v.id("organizations")),
    userId: v.optional(v.id("users")),
    type: v.union(v.literal(PurchaseType.SUBSCRIPTION), v.literal(PurchaseType.ONE_TIME)),
    customerId: v.string(),
    subscriptionId: v.optional(v.string()),
    productId: v.string(),
    status: v.optional(v.string()),
  }).index("by_subscriptionId", ["subscriptionId"])
    .index("by_organizationId", ["organizationId"])
    .index("by_userId", ["userId"]),

  aiChats: defineTable({
    organizationId: v.optional(v.id("organizations")),
    userId: v.optional(v.id("users")),
    title: v.optional(v.string()),
    messages: v.array(
      v.object({
        role: v.union(v.literal(AiChatRole.USER), v.literal(AiChatRole.ASSISTANT)),
        content: v.string(),
      })
    ),
  })
    .index("by_organizationId", ["organizationId"])
    .index("by_userId", ["userId"]),
});