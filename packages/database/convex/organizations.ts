import { query, mutation, internalMutation } from "./_generated/server";
import { v } from "convex/values";

// Get all organizations
export const getOrganizations = query({
  args: {
    limit: v.optional(v.number()),
    offset: v.optional(v.number()),
    search: v.optional(v.string()),
  },
  handler: async (ctx, { limit = 10, offset = 0, search }) => {
    const organizations = await ctx.db.query("organizations").fullTableScan().collect();

    const filtered = search
      ? organizations.filter((org) =>
          org.name.toLowerCase().includes(search.toLowerCase())
        )
      : organizations;

    const paginated = filtered.slice(offset, offset + limit);

    const organizationsWithMemberCount = await Promise.all(
      paginated.map(async (org) => {
        const members = await ctx.db
          .query("members")
          .withIndex("by_organizationId_userId", (q) => q.eq("organizationId", org._id))
          .collect();
        return { ...org, membersCount: members.length };
      })
    );

    return organizationsWithMemberCount;
  },
});

// Count all organizations
export const countAllOrganizations = query({
  args: {
    limit: v.optional(v.number()),
    offset: v.optional(v.number()),
    query: v.optional(v.string()),
  },
  handler: async (ctx, { limit = 10, offset = 0, query }) => {
    const organizations = await ctx.db.query("organizations").fullTableScan().collect();

    const filtered = query
      ? organizations.filter((org) =>
          org.name.toLowerCase().includes(query.toLowerCase())
        )
      : organizations;

    return filtered.slice(offset, offset + limit).length;
  },
});

// Get an organization by its ID
export const getOrganizationById = query({
  args: { id: v.id("organizations") },
  handler: async (ctx, args) => {
    const organization = await ctx.db.get(args.id);
    if (!organization) return null;

    const members = await ctx.db
      .query("members")
      .withIndex("by_organizationId_userId", (q) => q.eq("organizationId", args.id))
      .collect();

    const invitations = await ctx.db
      .query("invitations")
      .withIndex("by_organizationId", (q) => q.eq("organizationId", args.id))
      .collect();

    return { ...organization, members, invitations };
  },
});

// Get an invitation by its ID
export const getInvitationById = query({
  args: { id: v.id("invitations") },
  handler: async (ctx, args) => {
    const invitation = await ctx.db.get(args.id);
    if (!invitation) return null;

    const organization = await ctx.db.get(invitation.organizationId);
    return { ...invitation, organization };
  },
});

// Get an organization by its slug
export const getOrganizationBySlug = query({
  args: { slug: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("organizations")
      .withIndex("by_slug", (q) => q.eq("slug", args.slug))
      .first();
  },
});

// Get an organization membership by organization ID and user ID
export const getOrganizationMembership = query({
  args: { organizationId: v.id("organizations"), userId: v.id("users") },
  handler: async (ctx, args) => {
    const member = await ctx.db
      .query("members")
      .withIndex("by_organizationId_userId", (q) =>
        q.eq("organizationId", args.organizationId).eq("userId", args.userId)
      )
      .first();

    if (!member) return null;

    const organization = await ctx.db.get(member.organizationId);
    return { ...member, organization };
  },
});

// Get an organization with its purchases and members count
export const getOrganizationWithPurchasesAndMembersCount = query({
  args: { organizationId: v.id("organizations") },
  handler: async (ctx, args) => {
    const organization = await ctx.db.get(args.organizationId);
    if (!organization) return null;

    const purchases = await ctx.db
      .query("purchases")
      .withIndex("by_organizationId", (q) =>
        q.eq("organizationId", args.organizationId)
      )
      .collect();

    const members = await ctx.db
      .query("members")
      .withIndex("by_organizationId_userId", (q) =>
        q.eq("organizationId", args.organizationId)
      )
      .collect();

    return { ...organization, purchases, membersCount: members.length };
  },
});

// Get a pending invitation by email
export const getPendingInvitationByEmail = query({
  args: { email: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("invitations")
      .withIndex("by_email_and_status", (q) =>
        q.eq("email", args.email).eq("status", "pending")
      )
      .first();
  },
});

// Update an organization
export const updateOrganization = mutation({
  args: {
    id: v.id("organizations"),
    name: v.optional(v.string()),
    slug: v.optional(v.string()),
    logo: v.optional(v.string()),
    metadata: v.optional(v.string()),
    paymentsCustomerId: v.optional(v.string()),
  },
  handler: async (ctx, { id, ...rest }) => {
    await ctx.db.patch(id, rest);
  },
});

// Update an organization's payments customer ID
export const updateOrgPaymentsId = internalMutation({
  args: { id: v.id("organizations"), paymentsCustomerId: v.string() },
  handler: async (ctx, { id, paymentsCustomerId }) => {
    await ctx.db.patch(id, { paymentsCustomerId });
  },
});
