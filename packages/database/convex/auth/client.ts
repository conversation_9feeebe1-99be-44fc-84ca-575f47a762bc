import {
  twoFactorClient,
  magicLinkClient,
  adminClient,
  organizationClient,
  passkeyClient,
} from "better-auth/client/plugins";
import { createAuthClient } from "better-auth/react";
import { convexClient } from "@convex-dev/better-auth/client/plugins";

export const authClient = createAuthClient({
  plugins: [
    magicLinkClient(),
    twoFactorClient(),
    adminClient(),
    organizationClient(),
    passkeyClient(),
    convexClient(),
  ],
});

export type AuthClientErrorCodes = typeof authClient.$ERROR_CODES; 