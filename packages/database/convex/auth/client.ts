import {
  twoFactorClient,
  magicLinkClient,
  adminClient,
  organizationClient,
  passkeyClient,
} from "better-auth/client/plugins";
import { createAuthClient } from "better-auth/react";
import { convexClient, crossDomainClient } from "@convex-dev/better-auth/client/plugins";
import { getBaseUrl } from "@workspace/utils";

export const authClient = createAuthClient({
  baseURL: getBaseUrl(),
  plugins: [
    convexClient(),
    crossDomainClient(),
    magicLinkClient(),
    twoFactorClient(),
    adminClient(),
    organizationClient(),
    passkeyClient(),
  ],
});

export type AuthClientErrorCodes = typeof authClient.$ERROR_CODES;