export interface User {
  _id: string;
  name: string;
  email: string;
  emailVerified: boolean;
  image?: string;
  username?: string;
  role?: string;
  banned?: boolean;
  banReason?: string;
  banExpires?: number;
  onboardingComplete: boolean;
  paymentsCustomerId?: string;
  twoFactorEnabled?: boolean;
}

export interface Session {
  session: {
    _id: string;
    expiresAt: number;
    ipAddress?: string;
    userAgent?: string;
    userId: string;
    impersonatedBy?: string;
    activeOrganizationId?: string;
    token: string;
  };
  user: User;
} 