import type { Session } from "./types";

export function convertToConvexSession(authSession: any): Session {
  return {
    session: {
      _id: authSession.session.id,
      expiresAt: new Date(authSession.session.expiresAt).getTime(),
      ipAddress: authSession.session.ipAddress || undefined,
      userAgent: authSession.session.userAgent || undefined,
      userId: authSession.session.userId,
      token: authSession.session.token,
      impersonatedBy: undefined,
      activeOrganizationId: undefined,
    },
    user: {
      _id: authSession.user.id,
      name: authSession.user.name,
      email: authSession.user.email,
      emailVerified: authSession.user.emailVerified,
      image: authSession.user.image || undefined,
      username: undefined,
      role: undefined,
      banned: false,
      onboardingComplete: false,
    },
  }
} 