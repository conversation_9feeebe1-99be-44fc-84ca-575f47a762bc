import { config } from "@workspace/config";
import { convex } from "../../../helpers/client";
import { api } from "../../../_generated/api";
import type { BetterAuthPlugin } from "better-auth";
import { APIError, ERROR_MESSAGES, ERROR_CODES } from "../../../helpers/errors";
import { createAuthMiddleware } from "better-auth/plugins";

export const invitationOnly = () =>
	({
		id: "invitationOnly",
		hooks: {
			before: [
				{
					matcher: (context) =>
						context.path.startsWith("/sign-up/email"),
					handler: createAuthMiddleware(async (ctx) => {
						if (config.auth.enableSignup) return;

						const { email } = ctx.body;

						const hasInvitation =
							await convex.query(api.organizations.getPendingInvitationByEmail, { email });

						if (!hasInvitation) {
							throw new APIError(
								ERROR_CODES.BAD_REQUEST,
								ERROR_MESSAGES.INVALID_INVITATION
							);
						}
					}),
				},
			],
		},
		$ERROR_CODES: {
			INVALID_INVITATION: ERROR_MESSAGES.INVALID_INVITATION,
		},
	}) satisfies BetterAuthPlugin;