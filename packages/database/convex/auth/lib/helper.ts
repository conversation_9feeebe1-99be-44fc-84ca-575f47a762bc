import { OrganizationRole } from "../../helpers/enums";
import { Id } from "../../_generated/dataModel";

export function isOrganizationAdmin(
	organization?: any | null,
	user?: {
		_id: Id<"users">;
		role?: string | null;
	} | null,
) {
	const userOrganizationRole = organization?.members.find(
		(member: any) => member.userId === user?._id,
	)?.role;
	
	return (
		[OrganizationRole.ADMIN].includes(userOrganizationRole ?? "") ||
		user?.role === OrganizationRole.ADMIN
	);
} 