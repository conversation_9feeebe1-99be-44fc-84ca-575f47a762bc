/* eslint-disable */
/**
 * Generated `api` utility.
 *
 * THIS CODE IS AUTOMATICALLY GENERATED.
 *
 * To regenerate, run `npx convex dev`.
 * @module
 */

import type * as aiChats from "../aiChats.js";
import type * as auth_client from "../auth/client.js";
import type * as auth_index from "../auth/index.js";
import type * as auth_lib_helper from "../auth/lib/helper.js";
import type * as auth_lib_organization from "../auth/lib/organization.js";
import type * as auth_plugins_invitationOnly_index from "../auth/plugins/invitationOnly/index.js";
import type * as auth_types from "../auth/types.js";
import type * as auth_utils from "../auth/utils.js";
import type * as auth from "../auth.js";
import type * as helpers_client from "../helpers/client.js";
import type * as helpers_enums from "../helpers/enums.js";
import type * as helpers_errors from "../helpers/errors.js";
import type * as helpers_types from "../helpers/types.js";
import type * as helpers_zod from "../helpers/zod.js";
import type * as http from "../http.js";
import type * as organizations from "../organizations.js";
import type * as polar from "../polar.js";
import type * as purchases from "../purchases.js";
import type * as resend from "../resend.js";
import type * as seed from "../seed.js";
import type * as users from "../users.js";

import type {
  ApiFromModules,
  FilterApi,
  FunctionReference,
} from "convex/server";

/**
 * A utility for referencing Convex functions in your app's API.
 *
 * Usage:
 * ```js
 * const myFunctionReference = api.myModule.myFunction;
 * ```
 */
declare const fullApi: ApiFromModules<{
  aiChats: typeof aiChats;
  "auth/client": typeof auth_client;
  "auth/index": typeof auth_index;
  "auth/lib/helper": typeof auth_lib_helper;
  "auth/lib/organization": typeof auth_lib_organization;
  "auth/plugins/invitationOnly/index": typeof auth_plugins_invitationOnly_index;
  "auth/types": typeof auth_types;
  "auth/utils": typeof auth_utils;
  auth: typeof auth;
  "helpers/client": typeof helpers_client;
  "helpers/enums": typeof helpers_enums;
  "helpers/errors": typeof helpers_errors;
  "helpers/types": typeof helpers_types;
  "helpers/zod": typeof helpers_zod;
  http: typeof http;
  organizations: typeof organizations;
  polar: typeof polar;
  purchases: typeof purchases;
  resend: typeof resend;
  seed: typeof seed;
  users: typeof users;
}>;
declare const fullApiWithMounts: typeof fullApi;

export declare const api: FilterApi<
  typeof fullApiWithMounts,
  FunctionReference<any, "public">
>;
export declare const internal: FilterApi<
  typeof fullApiWithMounts,
  FunctionReference<any, "internal">
>;

export declare const components: {
  polar: {
    lib: {
      createProduct: FunctionReference<
        "mutation",
        "internal",
        {
          product: {
            createdAt: string;
            description: string | null;
            id: string;
            isArchived: boolean;
            isRecurring: boolean;
            medias: Array<{
              checksumEtag: string | null;
              checksumSha256Base64: string | null;
              checksumSha256Hex: string | null;
              createdAt: string;
              id: string;
              isUploaded: boolean;
              lastModifiedAt: string | null;
              mimeType: string;
              name: string;
              organizationId: string;
              path: string;
              publicUrl: string;
              service?: string;
              size: number;
              sizeReadable: string;
              storageVersion: string | null;
              version: string | null;
            }>;
            metadata?: Record<string, any>;
            modifiedAt: string | null;
            name: string;
            organizationId: string;
            prices: Array<{
              amountType?: string;
              createdAt: string;
              id: string;
              isArchived: boolean;
              modifiedAt: string | null;
              priceAmount?: number;
              priceCurrency?: string;
              productId: string;
              recurringInterval?: "month" | "year" | null;
              type?: string;
            }>;
            recurringInterval?: "month" | "year" | null;
          };
        },
        any
      >;
      createSubscription: FunctionReference<
        "mutation",
        "internal",
        {
          subscription: {
            amount: number | null;
            cancelAtPeriodEnd: boolean;
            checkoutId: string | null;
            createdAt: string;
            currency: string | null;
            currentPeriodEnd: string | null;
            currentPeriodStart: string;
            customerCancellationComment?: string | null;
            customerCancellationReason?: string | null;
            customerId: string;
            endedAt: string | null;
            id: string;
            metadata: Record<string, any>;
            modifiedAt: string | null;
            priceId?: string;
            productId: string;
            recurringInterval: "month" | "year" | null;
            startedAt: string | null;
            status: string;
          };
        },
        any
      >;
      getCurrentSubscription: FunctionReference<
        "query",
        "internal",
        { userId: string },
        {
          amount: number | null;
          cancelAtPeriodEnd: boolean;
          checkoutId: string | null;
          createdAt: string;
          currency: string | null;
          currentPeriodEnd: string | null;
          currentPeriodStart: string;
          customerCancellationComment?: string | null;
          customerCancellationReason?: string | null;
          customerId: string;
          endedAt: string | null;
          id: string;
          metadata: Record<string, any>;
          modifiedAt: string | null;
          priceId?: string;
          product: {
            createdAt: string;
            description: string | null;
            id: string;
            isArchived: boolean;
            isRecurring: boolean;
            medias: Array<{
              checksumEtag: string | null;
              checksumSha256Base64: string | null;
              checksumSha256Hex: string | null;
              createdAt: string;
              id: string;
              isUploaded: boolean;
              lastModifiedAt: string | null;
              mimeType: string;
              name: string;
              organizationId: string;
              path: string;
              publicUrl: string;
              service?: string;
              size: number;
              sizeReadable: string;
              storageVersion: string | null;
              version: string | null;
            }>;
            metadata?: Record<string, any>;
            modifiedAt: string | null;
            name: string;
            organizationId: string;
            prices: Array<{
              amountType?: string;
              createdAt: string;
              id: string;
              isArchived: boolean;
              modifiedAt: string | null;
              priceAmount?: number;
              priceCurrency?: string;
              productId: string;
              recurringInterval?: "month" | "year" | null;
              type?: string;
            }>;
            recurringInterval?: "month" | "year" | null;
          };
          productId: string;
          recurringInterval: "month" | "year" | null;
          startedAt: string | null;
          status: string;
        } | null
      >;
      getCustomerByUserId: FunctionReference<
        "query",
        "internal",
        { userId: string },
        { id: string; metadata?: Record<string, any>; userId: string } | null
      >;
      getProduct: FunctionReference<
        "query",
        "internal",
        { id: string },
        {
          createdAt: string;
          description: string | null;
          id: string;
          isArchived: boolean;
          isRecurring: boolean;
          medias: Array<{
            checksumEtag: string | null;
            checksumSha256Base64: string | null;
            checksumSha256Hex: string | null;
            createdAt: string;
            id: string;
            isUploaded: boolean;
            lastModifiedAt: string | null;
            mimeType: string;
            name: string;
            organizationId: string;
            path: string;
            publicUrl: string;
            service?: string;
            size: number;
            sizeReadable: string;
            storageVersion: string | null;
            version: string | null;
          }>;
          metadata?: Record<string, any>;
          modifiedAt: string | null;
          name: string;
          organizationId: string;
          prices: Array<{
            amountType?: string;
            createdAt: string;
            id: string;
            isArchived: boolean;
            modifiedAt: string | null;
            priceAmount?: number;
            priceCurrency?: string;
            productId: string;
            recurringInterval?: "month" | "year" | null;
            type?: string;
          }>;
          recurringInterval?: "month" | "year" | null;
        } | null
      >;
      getSubscription: FunctionReference<
        "query",
        "internal",
        { id: string },
        {
          amount: number | null;
          cancelAtPeriodEnd: boolean;
          checkoutId: string | null;
          createdAt: string;
          currency: string | null;
          currentPeriodEnd: string | null;
          currentPeriodStart: string;
          customerCancellationComment?: string | null;
          customerCancellationReason?: string | null;
          customerId: string;
          endedAt: string | null;
          id: string;
          metadata: Record<string, any>;
          modifiedAt: string | null;
          priceId?: string;
          productId: string;
          recurringInterval: "month" | "year" | null;
          startedAt: string | null;
          status: string;
        } | null
      >;
      insertCustomer: FunctionReference<
        "mutation",
        "internal",
        { id: string; metadata?: Record<string, any>; userId: string },
        string
      >;
      listCustomerSubscriptions: FunctionReference<
        "query",
        "internal",
        { customerId: string },
        Array<{
          amount: number | null;
          cancelAtPeriodEnd: boolean;
          checkoutId: string | null;
          createdAt: string;
          currency: string | null;
          currentPeriodEnd: string | null;
          currentPeriodStart: string;
          customerCancellationComment?: string | null;
          customerCancellationReason?: string | null;
          customerId: string;
          endedAt: string | null;
          id: string;
          metadata: Record<string, any>;
          modifiedAt: string | null;
          priceId?: string;
          productId: string;
          recurringInterval: "month" | "year" | null;
          startedAt: string | null;
          status: string;
        }>
      >;
      listProducts: FunctionReference<
        "query",
        "internal",
        { includeArchived?: boolean },
        Array<{
          createdAt: string;
          description: string | null;
          id: string;
          isArchived: boolean;
          isRecurring: boolean;
          medias: Array<{
            checksumEtag: string | null;
            checksumSha256Base64: string | null;
            checksumSha256Hex: string | null;
            createdAt: string;
            id: string;
            isUploaded: boolean;
            lastModifiedAt: string | null;
            mimeType: string;
            name: string;
            organizationId: string;
            path: string;
            publicUrl: string;
            service?: string;
            size: number;
            sizeReadable: string;
            storageVersion: string | null;
            version: string | null;
          }>;
          metadata?: Record<string, any>;
          modifiedAt: string | null;
          name: string;
          organizationId: string;
          priceAmount?: number;
          prices: Array<{
            amountType?: string;
            createdAt: string;
            id: string;
            isArchived: boolean;
            modifiedAt: string | null;
            priceAmount?: number;
            priceCurrency?: string;
            productId: string;
            recurringInterval?: "month" | "year" | null;
            type?: string;
          }>;
          recurringInterval?: "month" | "year" | null;
        }>
      >;
      listUserSubscriptions: FunctionReference<
        "query",
        "internal",
        { userId: string },
        Array<{
          amount: number | null;
          cancelAtPeriodEnd: boolean;
          checkoutId: string | null;
          createdAt: string;
          currency: string | null;
          currentPeriodEnd: string | null;
          currentPeriodStart: string;
          customerCancellationComment?: string | null;
          customerCancellationReason?: string | null;
          customerId: string;
          endedAt: string | null;
          id: string;
          metadata: Record<string, any>;
          modifiedAt: string | null;
          priceId?: string;
          product: {
            createdAt: string;
            description: string | null;
            id: string;
            isArchived: boolean;
            isRecurring: boolean;
            medias: Array<{
              checksumEtag: string | null;
              checksumSha256Base64: string | null;
              checksumSha256Hex: string | null;
              createdAt: string;
              id: string;
              isUploaded: boolean;
              lastModifiedAt: string | null;
              mimeType: string;
              name: string;
              organizationId: string;
              path: string;
              publicUrl: string;
              service?: string;
              size: number;
              sizeReadable: string;
              storageVersion: string | null;
              version: string | null;
            }>;
            metadata?: Record<string, any>;
            modifiedAt: string | null;
            name: string;
            organizationId: string;
            prices: Array<{
              amountType?: string;
              createdAt: string;
              id: string;
              isArchived: boolean;
              modifiedAt: string | null;
              priceAmount?: number;
              priceCurrency?: string;
              productId: string;
              recurringInterval?: "month" | "year" | null;
              type?: string;
            }>;
            recurringInterval?: "month" | "year" | null;
          } | null;
          productId: string;
          recurringInterval: "month" | "year" | null;
          startedAt: string | null;
          status: string;
        }>
      >;
      syncProducts: FunctionReference<
        "action",
        "internal",
        { polarAccessToken: string; server: "sandbox" | "production" },
        any
      >;
      updateProduct: FunctionReference<
        "mutation",
        "internal",
        {
          product: {
            createdAt: string;
            description: string | null;
            id: string;
            isArchived: boolean;
            isRecurring: boolean;
            medias: Array<{
              checksumEtag: string | null;
              checksumSha256Base64: string | null;
              checksumSha256Hex: string | null;
              createdAt: string;
              id: string;
              isUploaded: boolean;
              lastModifiedAt: string | null;
              mimeType: string;
              name: string;
              organizationId: string;
              path: string;
              publicUrl: string;
              service?: string;
              size: number;
              sizeReadable: string;
              storageVersion: string | null;
              version: string | null;
            }>;
            metadata?: Record<string, any>;
            modifiedAt: string | null;
            name: string;
            organizationId: string;
            prices: Array<{
              amountType?: string;
              createdAt: string;
              id: string;
              isArchived: boolean;
              modifiedAt: string | null;
              priceAmount?: number;
              priceCurrency?: string;
              productId: string;
              recurringInterval?: "month" | "year" | null;
              type?: string;
            }>;
            recurringInterval?: "month" | "year" | null;
          };
        },
        any
      >;
      updateProducts: FunctionReference<
        "mutation",
        "internal",
        {
          polarAccessToken: string;
          products: Array<{
            createdAt: string;
            description: string | null;
            id: string;
            isArchived: boolean;
            isRecurring: boolean;
            medias: Array<{
              checksumEtag: string | null;
              checksumSha256Base64: string | null;
              checksumSha256Hex: string | null;
              createdAt: string;
              id: string;
              isUploaded: boolean;
              lastModifiedAt: string | null;
              mimeType: string;
              name: string;
              organizationId: string;
              path: string;
              publicUrl: string;
              service?: string;
              size: number;
              sizeReadable: string;
              storageVersion: string | null;
              version: string | null;
            }>;
            metadata?: Record<string, any>;
            modifiedAt: string | null;
            name: string;
            organizationId: string;
            prices: Array<{
              amountType?: string;
              createdAt: string;
              id: string;
              isArchived: boolean;
              modifiedAt: string | null;
              priceAmount?: number;
              priceCurrency?: string;
              productId: string;
              recurringInterval?: "month" | "year" | null;
              type?: string;
            }>;
            recurringInterval?: "month" | "year" | null;
          }>;
        },
        any
      >;
      updateSubscription: FunctionReference<
        "mutation",
        "internal",
        {
          subscription: {
            amount: number | null;
            cancelAtPeriodEnd: boolean;
            checkoutId: string | null;
            createdAt: string;
            currency: string | null;
            currentPeriodEnd: string | null;
            currentPeriodStart: string;
            customerCancellationComment?: string | null;
            customerCancellationReason?: string | null;
            customerId: string;
            endedAt: string | null;
            id: string;
            metadata: Record<string, any>;
            modifiedAt: string | null;
            priceId?: string;
            productId: string;
            recurringInterval: "month" | "year" | null;
            startedAt: string | null;
            status: string;
          };
        },
        any
      >;
      upsertCustomer: FunctionReference<
        "mutation",
        "internal",
        { id: string; metadata?: Record<string, any>; userId: string },
        string
      >;
    };
  };
  resend: {
    lib: {
      cancelEmail: FunctionReference<
        "mutation",
        "internal",
        { emailId: string },
        null
      >;
      get: FunctionReference<"query", "internal", { emailId: string }, any>;
      getStatus: FunctionReference<
        "query",
        "internal",
        { emailId: string },
        {
          complained: boolean;
          errorMessage: string | null;
          opened: boolean;
          status:
            | "waiting"
            | "queued"
            | "cancelled"
            | "sent"
            | "delivered"
            | "delivery_delayed"
            | "bounced";
        }
      >;
      handleEmailEvent: FunctionReference<
        "mutation",
        "internal",
        { event: any },
        null
      >;
      sendEmail: FunctionReference<
        "mutation",
        "internal",
        {
          from: string;
          headers?: Array<{ name: string; value: string }>;
          html?: string;
          options: {
            apiKey: string;
            initialBackoffMs: number;
            onEmailEvent?: { fnHandle: string };
            retryAttempts: number;
            testMode: boolean;
          };
          replyTo?: Array<string>;
          subject: string;
          text?: string;
          to: string;
        },
        string
      >;
    };
  };
  betterAuth: {
    adapterTest: {
      count: FunctionReference<"query", "internal", any, any>;
      create: FunctionReference<"mutation", "internal", any, any>;
      delete: FunctionReference<"mutation", "internal", any, any>;
      deleteMany: FunctionReference<"mutation", "internal", any, any>;
      findMany: FunctionReference<"query", "internal", any, any>;
      findOne: FunctionReference<"query", "internal", any, any>;
      isAuthenticated: FunctionReference<"query", "internal", {}, any>;
      update: FunctionReference<"mutation", "internal", any, any>;
      updateMany: FunctionReference<"mutation", "internal", any, any>;
    };
    lib: {
      create: FunctionReference<
        "mutation",
        "internal",
        {
          input:
            | {
                data: {
                  banExpires?: number;
                  banReason?: string;
                  banned?: boolean;
                  createdAt: number;
                  displayUsername?: string;
                  email: string;
                  emailVerified: boolean;
                  image?: string;
                  isAnonymous?: boolean;
                  name: string;
                  phoneNumber?: string;
                  phoneNumberVerified?: boolean;
                  role?: string;
                  stripeCustomerId?: string;
                  twoFactorEnabled?: boolean;
                  updatedAt: number;
                  userId?: string;
                  username?: string;
                };
                model: "user";
                where?: Array<{
                  connector?: "AND" | "OR";
                  field: string;
                  operator?:
                    | "lt"
                    | "lte"
                    | "gt"
                    | "gte"
                    | "eq"
                    | "in"
                    | "ne"
                    | "contains"
                    | "starts_with"
                    | "ends_with";
                  value:
                    | string
                    | number
                    | boolean
                    | Array<string>
                    | Array<number>
                    | null;
                }>;
              }
            | {
                data: {
                  activeOrganizationId?: string;
                  createdAt: number;
                  expiresAt: number;
                  impersonatedBy?: string;
                  ipAddress?: string;
                  token: string;
                  updatedAt: number;
                  userAgent?: string;
                  userId: string;
                };
                model: "session";
                where?: Array<{
                  connector?: "AND" | "OR";
                  field: string;
                  operator?:
                    | "lt"
                    | "lte"
                    | "gt"
                    | "gte"
                    | "eq"
                    | "in"
                    | "ne"
                    | "contains"
                    | "starts_with"
                    | "ends_with";
                  value:
                    | string
                    | number
                    | boolean
                    | Array<string>
                    | Array<number>
                    | null;
                }>;
              }
            | {
                data: {
                  accessToken?: string;
                  accessTokenExpiresAt?: number;
                  accountId: string;
                  createdAt: number;
                  idToken?: string;
                  password?: string;
                  providerId: string;
                  refreshToken?: string;
                  refreshTokenExpiresAt?: number;
                  scope?: string;
                  updatedAt: number;
                  userId: string;
                };
                model: "account";
                where?: Array<{
                  connector?: "AND" | "OR";
                  field: string;
                  operator?:
                    | "lt"
                    | "lte"
                    | "gt"
                    | "gte"
                    | "eq"
                    | "in"
                    | "ne"
                    | "contains"
                    | "starts_with"
                    | "ends_with";
                  value:
                    | string
                    | number
                    | boolean
                    | Array<string>
                    | Array<number>
                    | null;
                }>;
              }
            | {
                data: {
                  createdAt?: number;
                  expiresAt: number;
                  identifier: string;
                  updatedAt?: number;
                  value: string;
                };
                model: "verification";
                where?: Array<{
                  connector?: "AND" | "OR";
                  field: string;
                  operator?:
                    | "lt"
                    | "lte"
                    | "gt"
                    | "gte"
                    | "eq"
                    | "in"
                    | "ne"
                    | "contains"
                    | "starts_with"
                    | "ends_with";
                  value:
                    | string
                    | number
                    | boolean
                    | Array<string>
                    | Array<number>
                    | null;
                }>;
              }
            | {
                data: { backupCodes: string; secret: string; userId: string };
                model: "twoFactor";
                where?: Array<{
                  connector?: "AND" | "OR";
                  field: string;
                  operator?:
                    | "lt"
                    | "lte"
                    | "gt"
                    | "gte"
                    | "eq"
                    | "in"
                    | "ne"
                    | "contains"
                    | "starts_with"
                    | "ends_with";
                  value:
                    | string
                    | number
                    | boolean
                    | Array<string>
                    | Array<number>
                    | null;
                }>;
              }
            | {
                data: {
                  aaguid?: string;
                  backedUp: boolean;
                  counter: number;
                  createdAt?: number;
                  credentialID: string;
                  deviceType: string;
                  name?: string;
                  publicKey: string;
                  transports?: string;
                  userId: string;
                };
                model: "passkey";
                where?: Array<{
                  connector?: "AND" | "OR";
                  field: string;
                  operator?:
                    | "lt"
                    | "lte"
                    | "gt"
                    | "gte"
                    | "eq"
                    | "in"
                    | "ne"
                    | "contains"
                    | "starts_with"
                    | "ends_with";
                  value:
                    | string
                    | number
                    | boolean
                    | Array<string>
                    | Array<number>
                    | null;
                }>;
              }
            | {
                data: {
                  createdAt: number;
                  enabled?: boolean;
                  expiresAt?: number;
                  key: string;
                  lastRefillAt?: number;
                  lastRequest?: number;
                  metadata?: string;
                  name?: string;
                  permissions?: string;
                  prefix?: string;
                  rateLimitEnabled?: boolean;
                  rateLimitMax?: number;
                  rateLimitTimeWindow?: number;
                  refillAmount?: number;
                  refillInterval?: number;
                  remaining?: number;
                  requestCount?: number;
                  start?: string;
                  updatedAt: number;
                  userId: string;
                };
                model: "apikey";
                where?: Array<{
                  connector?: "AND" | "OR";
                  field: string;
                  operator?:
                    | "lt"
                    | "lte"
                    | "gt"
                    | "gte"
                    | "eq"
                    | "in"
                    | "ne"
                    | "contains"
                    | "starts_with"
                    | "ends_with";
                  value:
                    | string
                    | number
                    | boolean
                    | Array<string>
                    | Array<number>
                    | null;
                }>;
              }
            | {
                data: {
                  clientId?: string;
                  clientSecret?: string;
                  createdAt?: number;
                  disabled?: boolean;
                  icon?: string;
                  metadata?: string;
                  name?: string;
                  redirectURLs?: string;
                  type?: string;
                  updatedAt?: number;
                  userId?: string;
                };
                model: "oauthApplication";
                where?: Array<{
                  connector?: "AND" | "OR";
                  field: string;
                  operator?:
                    | "lt"
                    | "lte"
                    | "gt"
                    | "gte"
                    | "eq"
                    | "in"
                    | "ne"
                    | "contains"
                    | "starts_with"
                    | "ends_with";
                  value:
                    | string
                    | number
                    | boolean
                    | Array<string>
                    | Array<number>
                    | null;
                }>;
              }
            | {
                data: {
                  accessToken?: string;
                  accessTokenExpiresAt?: number;
                  clientId?: string;
                  createdAt?: number;
                  refreshToken?: string;
                  refreshTokenExpiresAt?: number;
                  scopes?: string;
                  updatedAt?: number;
                  userId?: string;
                };
                model: "oauthAccessToken";
                where?: Array<{
                  connector?: "AND" | "OR";
                  field: string;
                  operator?:
                    | "lt"
                    | "lte"
                    | "gt"
                    | "gte"
                    | "eq"
                    | "in"
                    | "ne"
                    | "contains"
                    | "starts_with"
                    | "ends_with";
                  value:
                    | string
                    | number
                    | boolean
                    | Array<string>
                    | Array<number>
                    | null;
                }>;
              }
            | {
                data: {
                  clientId?: string;
                  consentGiven?: boolean;
                  createdAt?: number;
                  scopes?: string;
                  updatedAt?: number;
                  userId?: string;
                };
                model: "oauthConsent";
                where?: Array<{
                  connector?: "AND" | "OR";
                  field: string;
                  operator?:
                    | "lt"
                    | "lte"
                    | "gt"
                    | "gte"
                    | "eq"
                    | "in"
                    | "ne"
                    | "contains"
                    | "starts_with"
                    | "ends_with";
                  value:
                    | string
                    | number
                    | boolean
                    | Array<string>
                    | Array<number>
                    | null;
                }>;
              }
            | {
                data: {
                  createdAt: number;
                  logo?: string;
                  metadata?: string;
                  name: string;
                  slug?: string;
                };
                model: "organization";
                where?: Array<{
                  connector?: "AND" | "OR";
                  field: string;
                  operator?:
                    | "lt"
                    | "lte"
                    | "gt"
                    | "gte"
                    | "eq"
                    | "in"
                    | "ne"
                    | "contains"
                    | "starts_with"
                    | "ends_with";
                  value:
                    | string
                    | number
                    | boolean
                    | Array<string>
                    | Array<number>
                    | null;
                }>;
              }
            | {
                data: {
                  createdAt: number;
                  organizationId: string;
                  role: string;
                  teamId?: string;
                  userId: string;
                };
                model: "member";
                where?: Array<{
                  connector?: "AND" | "OR";
                  field: string;
                  operator?:
                    | "lt"
                    | "lte"
                    | "gt"
                    | "gte"
                    | "eq"
                    | "in"
                    | "ne"
                    | "contains"
                    | "starts_with"
                    | "ends_with";
                  value:
                    | string
                    | number
                    | boolean
                    | Array<string>
                    | Array<number>
                    | null;
                }>;
              }
            | {
                data: {
                  email: string;
                  expiresAt: number;
                  inviterId: string;
                  organizationId: string;
                  role?: string;
                  status: string;
                  teamId?: string;
                };
                model: "invitation";
                where?: Array<{
                  connector?: "AND" | "OR";
                  field: string;
                  operator?:
                    | "lt"
                    | "lte"
                    | "gt"
                    | "gte"
                    | "eq"
                    | "in"
                    | "ne"
                    | "contains"
                    | "starts_with"
                    | "ends_with";
                  value:
                    | string
                    | number
                    | boolean
                    | Array<string>
                    | Array<number>
                    | null;
                }>;
              }
            | {
                data: {
                  createdAt: number;
                  name: string;
                  organizationId: string;
                  updatedAt?: number;
                };
                model: "team";
                where?: Array<{
                  connector?: "AND" | "OR";
                  field: string;
                  operator?:
                    | "lt"
                    | "lte"
                    | "gt"
                    | "gte"
                    | "eq"
                    | "in"
                    | "ne"
                    | "contains"
                    | "starts_with"
                    | "ends_with";
                  value:
                    | string
                    | number
                    | boolean
                    | Array<string>
                    | Array<number>
                    | null;
                }>;
              }
            | {
                data: {
                  domain: string;
                  issuer: string;
                  oidcConfig?: string;
                  organizationId?: string;
                  providerId: string;
                  samlConfig?: string;
                  userId?: string;
                };
                model: "ssoProvider";
                where?: Array<{
                  connector?: "AND" | "OR";
                  field: string;
                  operator?:
                    | "lt"
                    | "lte"
                    | "gt"
                    | "gte"
                    | "eq"
                    | "in"
                    | "ne"
                    | "contains"
                    | "starts_with"
                    | "ends_with";
                  value:
                    | string
                    | number
                    | boolean
                    | Array<string>
                    | Array<number>
                    | null;
                }>;
              }
            | {
                data: {
                  createdAt: number;
                  privateKey: string;
                  publicKey: string;
                };
                model: "jwks";
                where?: Array<{
                  connector?: "AND" | "OR";
                  field: string;
                  operator?:
                    | "lt"
                    | "lte"
                    | "gt"
                    | "gte"
                    | "eq"
                    | "in"
                    | "ne"
                    | "contains"
                    | "starts_with"
                    | "ends_with";
                  value:
                    | string
                    | number
                    | boolean
                    | Array<string>
                    | Array<number>
                    | null;
                }>;
              }
            | {
                data: {
                  cancelAtPeriodEnd?: boolean;
                  periodEnd?: number;
                  periodStart?: number;
                  plan: string;
                  referenceId: string;
                  seats?: number;
                  status?: string;
                  stripeCustomerId?: string;
                  stripeSubscriptionId?: string;
                };
                model: "subscription";
                where?: Array<{
                  connector?: "AND" | "OR";
                  field: string;
                  operator?:
                    | "lt"
                    | "lte"
                    | "gt"
                    | "gte"
                    | "eq"
                    | "in"
                    | "ne"
                    | "contains"
                    | "starts_with"
                    | "ends_with";
                  value:
                    | string
                    | number
                    | boolean
                    | Array<string>
                    | Array<number>
                    | null;
                }>;
              }
            | {
                data: { count?: number; key?: string; lastRequest?: number };
                model: "rateLimit";
                where?: Array<{
                  connector?: "AND" | "OR";
                  field: string;
                  operator?:
                    | "lt"
                    | "lte"
                    | "gt"
                    | "gte"
                    | "eq"
                    | "in"
                    | "ne"
                    | "contains"
                    | "starts_with"
                    | "ends_with";
                  value:
                    | string
                    | number
                    | boolean
                    | Array<string>
                    | Array<number>
                    | null;
                }>;
              };
        },
        any
      >;
      deleteMany: FunctionReference<
        "mutation",
        "internal",
        {
          limit?: number;
          model: string;
          offset?: number;
          paginationOpts: {
            cursor: string | null;
            endCursor?: string | null;
            id?: number;
            maximumBytesRead?: number;
            maximumRowsRead?: number;
            numItems: number;
          };
          select?: Array<string>;
          sortBy?: { direction: "asc" | "desc"; field: string };
          unique?: boolean;
          where?: Array<{
            connector?: "AND" | "OR";
            field: string;
            operator?:
              | "lt"
              | "lte"
              | "gt"
              | "gte"
              | "eq"
              | "in"
              | "ne"
              | "contains"
              | "starts_with"
              | "ends_with";
            value:
              | string
              | number
              | boolean
              | Array<string>
              | Array<number>
              | null;
          }>;
        },
        any
      >;
      deleteOne: FunctionReference<
        "mutation",
        "internal",
        {
          limit?: number;
          model: string;
          offset?: number;
          select?: Array<string>;
          sortBy?: { direction: "asc" | "desc"; field: string };
          unique?: boolean;
          where?: Array<{
            connector?: "AND" | "OR";
            field: string;
            operator?:
              | "lt"
              | "lte"
              | "gt"
              | "gte"
              | "eq"
              | "in"
              | "ne"
              | "contains"
              | "starts_with"
              | "ends_with";
            value:
              | string
              | number
              | boolean
              | Array<string>
              | Array<number>
              | null;
          }>;
        },
        any
      >;
      findMany: FunctionReference<
        "query",
        "internal",
        {
          limit?: number;
          model: string;
          offset?: number;
          paginationOpts: {
            cursor: string | null;
            endCursor?: string | null;
            id?: number;
            maximumBytesRead?: number;
            maximumRowsRead?: number;
            numItems: number;
          };
          select?: Array<string>;
          sortBy?: { direction: "asc" | "desc"; field: string };
          unique?: boolean;
          where?: Array<{
            connector?: "AND" | "OR";
            field: string;
            operator?:
              | "lt"
              | "lte"
              | "gt"
              | "gte"
              | "eq"
              | "in"
              | "ne"
              | "contains"
              | "starts_with"
              | "ends_with";
            value:
              | string
              | number
              | boolean
              | Array<string>
              | Array<number>
              | null;
          }>;
        },
        any
      >;
      findOne: FunctionReference<
        "query",
        "internal",
        {
          limit?: number;
          model: string;
          offset?: number;
          select?: Array<string>;
          sortBy?: { direction: "asc" | "desc"; field: string };
          unique?: boolean;
          where?: Array<{
            connector?: "AND" | "OR";
            field: string;
            operator?:
              | "lt"
              | "lte"
              | "gt"
              | "gte"
              | "eq"
              | "in"
              | "ne"
              | "contains"
              | "starts_with"
              | "ends_with";
            value:
              | string
              | number
              | boolean
              | Array<string>
              | Array<number>
              | null;
          }>;
        },
        any
      >;
      getCurrentSession: FunctionReference<"query", "internal", {}, any>;
      updateMany: FunctionReference<
        "mutation",
        "internal",
        {
          input:
            | {
                limit?: number;
                model: "user";
                offset?: number;
                paginationOpts: {
                  cursor: string | null;
                  endCursor?: string | null;
                  id?: number;
                  maximumBytesRead?: number;
                  maximumRowsRead?: number;
                  numItems: number;
                };
                select?: Array<string>;
                sortBy?: { direction: "asc" | "desc"; field: string };
                unique?: boolean;
                update: {
                  banExpires?: number;
                  banReason?: string;
                  banned?: boolean;
                  createdAt?: number;
                  displayUsername?: string;
                  email?: string;
                  emailVerified?: boolean;
                  image?: string;
                  isAnonymous?: boolean;
                  name?: string;
                  phoneNumber?: string;
                  phoneNumberVerified?: boolean;
                  role?: string;
                  stripeCustomerId?: string;
                  twoFactorEnabled?: boolean;
                  updatedAt?: number;
                  userId?: string;
                  username?: string;
                };
                where?: Array<{
                  connector?: "AND" | "OR";
                  field: string;
                  operator?:
                    | "lt"
                    | "lte"
                    | "gt"
                    | "gte"
                    | "eq"
                    | "in"
                    | "ne"
                    | "contains"
                    | "starts_with"
                    | "ends_with";
                  value:
                    | string
                    | number
                    | boolean
                    | Array<string>
                    | Array<number>
                    | null;
                }>;
              }
            | {
                limit?: number;
                model: "session";
                offset?: number;
                paginationOpts: {
                  cursor: string | null;
                  endCursor?: string | null;
                  id?: number;
                  maximumBytesRead?: number;
                  maximumRowsRead?: number;
                  numItems: number;
                };
                select?: Array<string>;
                sortBy?: { direction: "asc" | "desc"; field: string };
                unique?: boolean;
                update: {
                  activeOrganizationId?: string;
                  createdAt?: number;
                  expiresAt?: number;
                  impersonatedBy?: string;
                  ipAddress?: string;
                  token?: string;
                  updatedAt?: number;
                  userAgent?: string;
                  userId?: string;
                };
                where?: Array<{
                  connector?: "AND" | "OR";
                  field: string;
                  operator?:
                    | "lt"
                    | "lte"
                    | "gt"
                    | "gte"
                    | "eq"
                    | "in"
                    | "ne"
                    | "contains"
                    | "starts_with"
                    | "ends_with";
                  value:
                    | string
                    | number
                    | boolean
                    | Array<string>
                    | Array<number>
                    | null;
                }>;
              }
            | {
                limit?: number;
                model: "account";
                offset?: number;
                paginationOpts: {
                  cursor: string | null;
                  endCursor?: string | null;
                  id?: number;
                  maximumBytesRead?: number;
                  maximumRowsRead?: number;
                  numItems: number;
                };
                select?: Array<string>;
                sortBy?: { direction: "asc" | "desc"; field: string };
                unique?: boolean;
                update: {
                  accessToken?: string;
                  accessTokenExpiresAt?: number;
                  accountId?: string;
                  createdAt?: number;
                  idToken?: string;
                  password?: string;
                  providerId?: string;
                  refreshToken?: string;
                  refreshTokenExpiresAt?: number;
                  scope?: string;
                  updatedAt?: number;
                  userId?: string;
                };
                where?: Array<{
                  connector?: "AND" | "OR";
                  field: string;
                  operator?:
                    | "lt"
                    | "lte"
                    | "gt"
                    | "gte"
                    | "eq"
                    | "in"
                    | "ne"
                    | "contains"
                    | "starts_with"
                    | "ends_with";
                  value:
                    | string
                    | number
                    | boolean
                    | Array<string>
                    | Array<number>
                    | null;
                }>;
              }
            | {
                limit?: number;
                model: "verification";
                offset?: number;
                paginationOpts: {
                  cursor: string | null;
                  endCursor?: string | null;
                  id?: number;
                  maximumBytesRead?: number;
                  maximumRowsRead?: number;
                  numItems: number;
                };
                select?: Array<string>;
                sortBy?: { direction: "asc" | "desc"; field: string };
                unique?: boolean;
                update: {
                  createdAt?: number;
                  expiresAt?: number;
                  identifier?: string;
                  updatedAt?: number;
                  value?: string;
                };
                where?: Array<{
                  connector?: "AND" | "OR";
                  field: string;
                  operator?:
                    | "lt"
                    | "lte"
                    | "gt"
                    | "gte"
                    | "eq"
                    | "in"
                    | "ne"
                    | "contains"
                    | "starts_with"
                    | "ends_with";
                  value:
                    | string
                    | number
                    | boolean
                    | Array<string>
                    | Array<number>
                    | null;
                }>;
              }
            | {
                limit?: number;
                model: "twoFactor";
                offset?: number;
                paginationOpts: {
                  cursor: string | null;
                  endCursor?: string | null;
                  id?: number;
                  maximumBytesRead?: number;
                  maximumRowsRead?: number;
                  numItems: number;
                };
                select?: Array<string>;
                sortBy?: { direction: "asc" | "desc"; field: string };
                unique?: boolean;
                update: {
                  backupCodes?: string;
                  secret?: string;
                  userId?: string;
                };
                where?: Array<{
                  connector?: "AND" | "OR";
                  field: string;
                  operator?:
                    | "lt"
                    | "lte"
                    | "gt"
                    | "gte"
                    | "eq"
                    | "in"
                    | "ne"
                    | "contains"
                    | "starts_with"
                    | "ends_with";
                  value:
                    | string
                    | number
                    | boolean
                    | Array<string>
                    | Array<number>
                    | null;
                }>;
              }
            | {
                limit?: number;
                model: "passkey";
                offset?: number;
                paginationOpts: {
                  cursor: string | null;
                  endCursor?: string | null;
                  id?: number;
                  maximumBytesRead?: number;
                  maximumRowsRead?: number;
                  numItems: number;
                };
                select?: Array<string>;
                sortBy?: { direction: "asc" | "desc"; field: string };
                unique?: boolean;
                update: {
                  aaguid?: string;
                  backedUp?: boolean;
                  counter?: number;
                  createdAt?: number;
                  credentialID?: string;
                  deviceType?: string;
                  name?: string;
                  publicKey?: string;
                  transports?: string;
                  userId?: string;
                };
                where?: Array<{
                  connector?: "AND" | "OR";
                  field: string;
                  operator?:
                    | "lt"
                    | "lte"
                    | "gt"
                    | "gte"
                    | "eq"
                    | "in"
                    | "ne"
                    | "contains"
                    | "starts_with"
                    | "ends_with";
                  value:
                    | string
                    | number
                    | boolean
                    | Array<string>
                    | Array<number>
                    | null;
                }>;
              }
            | {
                limit?: number;
                model: "apikey";
                offset?: number;
                paginationOpts: {
                  cursor: string | null;
                  endCursor?: string | null;
                  id?: number;
                  maximumBytesRead?: number;
                  maximumRowsRead?: number;
                  numItems: number;
                };
                select?: Array<string>;
                sortBy?: { direction: "asc" | "desc"; field: string };
                unique?: boolean;
                update: {
                  createdAt?: number;
                  enabled?: boolean;
                  expiresAt?: number;
                  key?: string;
                  lastRefillAt?: number;
                  lastRequest?: number;
                  metadata?: string;
                  name?: string;
                  permissions?: string;
                  prefix?: string;
                  rateLimitEnabled?: boolean;
                  rateLimitMax?: number;
                  rateLimitTimeWindow?: number;
                  refillAmount?: number;
                  refillInterval?: number;
                  remaining?: number;
                  requestCount?: number;
                  start?: string;
                  updatedAt?: number;
                  userId?: string;
                };
                where?: Array<{
                  connector?: "AND" | "OR";
                  field: string;
                  operator?:
                    | "lt"
                    | "lte"
                    | "gt"
                    | "gte"
                    | "eq"
                    | "in"
                    | "ne"
                    | "contains"
                    | "starts_with"
                    | "ends_with";
                  value:
                    | string
                    | number
                    | boolean
                    | Array<string>
                    | Array<number>
                    | null;
                }>;
              }
            | {
                limit?: number;
                model: "oauthApplication";
                offset?: number;
                paginationOpts: {
                  cursor: string | null;
                  endCursor?: string | null;
                  id?: number;
                  maximumBytesRead?: number;
                  maximumRowsRead?: number;
                  numItems: number;
                };
                select?: Array<string>;
                sortBy?: { direction: "asc" | "desc"; field: string };
                unique?: boolean;
                update: {
                  clientId?: string;
                  clientSecret?: string;
                  createdAt?: number;
                  disabled?: boolean;
                  icon?: string;
                  metadata?: string;
                  name?: string;
                  redirectURLs?: string;
                  type?: string;
                  updatedAt?: number;
                  userId?: string;
                };
                where?: Array<{
                  connector?: "AND" | "OR";
                  field: string;
                  operator?:
                    | "lt"
                    | "lte"
                    | "gt"
                    | "gte"
                    | "eq"
                    | "in"
                    | "ne"
                    | "contains"
                    | "starts_with"
                    | "ends_with";
                  value:
                    | string
                    | number
                    | boolean
                    | Array<string>
                    | Array<number>
                    | null;
                }>;
              }
            | {
                limit?: number;
                model: "oauthAccessToken";
                offset?: number;
                paginationOpts: {
                  cursor: string | null;
                  endCursor?: string | null;
                  id?: number;
                  maximumBytesRead?: number;
                  maximumRowsRead?: number;
                  numItems: number;
                };
                select?: Array<string>;
                sortBy?: { direction: "asc" | "desc"; field: string };
                unique?: boolean;
                update: {
                  accessToken?: string;
                  accessTokenExpiresAt?: number;
                  clientId?: string;
                  createdAt?: number;
                  refreshToken?: string;
                  refreshTokenExpiresAt?: number;
                  scopes?: string;
                  updatedAt?: number;
                  userId?: string;
                };
                where?: Array<{
                  connector?: "AND" | "OR";
                  field: string;
                  operator?:
                    | "lt"
                    | "lte"
                    | "gt"
                    | "gte"
                    | "eq"
                    | "in"
                    | "ne"
                    | "contains"
                    | "starts_with"
                    | "ends_with";
                  value:
                    | string
                    | number
                    | boolean
                    | Array<string>
                    | Array<number>
                    | null;
                }>;
              }
            | {
                limit?: number;
                model: "oauthConsent";
                offset?: number;
                paginationOpts: {
                  cursor: string | null;
                  endCursor?: string | null;
                  id?: number;
                  maximumBytesRead?: number;
                  maximumRowsRead?: number;
                  numItems: number;
                };
                select?: Array<string>;
                sortBy?: { direction: "asc" | "desc"; field: string };
                unique?: boolean;
                update: {
                  clientId?: string;
                  consentGiven?: boolean;
                  createdAt?: number;
                  scopes?: string;
                  updatedAt?: number;
                  userId?: string;
                };
                where?: Array<{
                  connector?: "AND" | "OR";
                  field: string;
                  operator?:
                    | "lt"
                    | "lte"
                    | "gt"
                    | "gte"
                    | "eq"
                    | "in"
                    | "ne"
                    | "contains"
                    | "starts_with"
                    | "ends_with";
                  value:
                    | string
                    | number
                    | boolean
                    | Array<string>
                    | Array<number>
                    | null;
                }>;
              }
            | {
                limit?: number;
                model: "organization";
                offset?: number;
                paginationOpts: {
                  cursor: string | null;
                  endCursor?: string | null;
                  id?: number;
                  maximumBytesRead?: number;
                  maximumRowsRead?: number;
                  numItems: number;
                };
                select?: Array<string>;
                sortBy?: { direction: "asc" | "desc"; field: string };
                unique?: boolean;
                update: {
                  createdAt?: number;
                  logo?: string;
                  metadata?: string;
                  name?: string;
                  slug?: string;
                };
                where?: Array<{
                  connector?: "AND" | "OR";
                  field: string;
                  operator?:
                    | "lt"
                    | "lte"
                    | "gt"
                    | "gte"
                    | "eq"
                    | "in"
                    | "ne"
                    | "contains"
                    | "starts_with"
                    | "ends_with";
                  value:
                    | string
                    | number
                    | boolean
                    | Array<string>
                    | Array<number>
                    | null;
                }>;
              }
            | {
                limit?: number;
                model: "member";
                offset?: number;
                paginationOpts: {
                  cursor: string | null;
                  endCursor?: string | null;
                  id?: number;
                  maximumBytesRead?: number;
                  maximumRowsRead?: number;
                  numItems: number;
                };
                select?: Array<string>;
                sortBy?: { direction: "asc" | "desc"; field: string };
                unique?: boolean;
                update: {
                  createdAt?: number;
                  organizationId?: string;
                  role?: string;
                  teamId?: string;
                  userId?: string;
                };
                where?: Array<{
                  connector?: "AND" | "OR";
                  field: string;
                  operator?:
                    | "lt"
                    | "lte"
                    | "gt"
                    | "gte"
                    | "eq"
                    | "in"
                    | "ne"
                    | "contains"
                    | "starts_with"
                    | "ends_with";
                  value:
                    | string
                    | number
                    | boolean
                    | Array<string>
                    | Array<number>
                    | null;
                }>;
              }
            | {
                limit?: number;
                model: "invitation";
                offset?: number;
                paginationOpts: {
                  cursor: string | null;
                  endCursor?: string | null;
                  id?: number;
                  maximumBytesRead?: number;
                  maximumRowsRead?: number;
                  numItems: number;
                };
                select?: Array<string>;
                sortBy?: { direction: "asc" | "desc"; field: string };
                unique?: boolean;
                update: {
                  email?: string;
                  expiresAt?: number;
                  inviterId?: string;
                  organizationId?: string;
                  role?: string;
                  status?: string;
                  teamId?: string;
                };
                where?: Array<{
                  connector?: "AND" | "OR";
                  field: string;
                  operator?:
                    | "lt"
                    | "lte"
                    | "gt"
                    | "gte"
                    | "eq"
                    | "in"
                    | "ne"
                    | "contains"
                    | "starts_with"
                    | "ends_with";
                  value:
                    | string
                    | number
                    | boolean
                    | Array<string>
                    | Array<number>
                    | null;
                }>;
              }
            | {
                limit?: number;
                model: "team";
                offset?: number;
                paginationOpts: {
                  cursor: string | null;
                  endCursor?: string | null;
                  id?: number;
                  maximumBytesRead?: number;
                  maximumRowsRead?: number;
                  numItems: number;
                };
                select?: Array<string>;
                sortBy?: { direction: "asc" | "desc"; field: string };
                unique?: boolean;
                update: {
                  createdAt?: number;
                  name?: string;
                  organizationId?: string;
                  updatedAt?: number;
                };
                where?: Array<{
                  connector?: "AND" | "OR";
                  field: string;
                  operator?:
                    | "lt"
                    | "lte"
                    | "gt"
                    | "gte"
                    | "eq"
                    | "in"
                    | "ne"
                    | "contains"
                    | "starts_with"
                    | "ends_with";
                  value:
                    | string
                    | number
                    | boolean
                    | Array<string>
                    | Array<number>
                    | null;
                }>;
              }
            | {
                limit?: number;
                model: "ssoProvider";
                offset?: number;
                paginationOpts: {
                  cursor: string | null;
                  endCursor?: string | null;
                  id?: number;
                  maximumBytesRead?: number;
                  maximumRowsRead?: number;
                  numItems: number;
                };
                select?: Array<string>;
                sortBy?: { direction: "asc" | "desc"; field: string };
                unique?: boolean;
                update: {
                  domain?: string;
                  issuer?: string;
                  oidcConfig?: string;
                  organizationId?: string;
                  providerId?: string;
                  samlConfig?: string;
                  userId?: string;
                };
                where?: Array<{
                  connector?: "AND" | "OR";
                  field: string;
                  operator?:
                    | "lt"
                    | "lte"
                    | "gt"
                    | "gte"
                    | "eq"
                    | "in"
                    | "ne"
                    | "contains"
                    | "starts_with"
                    | "ends_with";
                  value:
                    | string
                    | number
                    | boolean
                    | Array<string>
                    | Array<number>
                    | null;
                }>;
              }
            | {
                limit?: number;
                model: "jwks";
                offset?: number;
                paginationOpts: {
                  cursor: string | null;
                  endCursor?: string | null;
                  id?: number;
                  maximumBytesRead?: number;
                  maximumRowsRead?: number;
                  numItems: number;
                };
                select?: Array<string>;
                sortBy?: { direction: "asc" | "desc"; field: string };
                unique?: boolean;
                update: {
                  createdAt?: number;
                  privateKey?: string;
                  publicKey?: string;
                };
                where?: Array<{
                  connector?: "AND" | "OR";
                  field: string;
                  operator?:
                    | "lt"
                    | "lte"
                    | "gt"
                    | "gte"
                    | "eq"
                    | "in"
                    | "ne"
                    | "contains"
                    | "starts_with"
                    | "ends_with";
                  value:
                    | string
                    | number
                    | boolean
                    | Array<string>
                    | Array<number>
                    | null;
                }>;
              }
            | {
                limit?: number;
                model: "subscription";
                offset?: number;
                paginationOpts: {
                  cursor: string | null;
                  endCursor?: string | null;
                  id?: number;
                  maximumBytesRead?: number;
                  maximumRowsRead?: number;
                  numItems: number;
                };
                select?: Array<string>;
                sortBy?: { direction: "asc" | "desc"; field: string };
                unique?: boolean;
                update: {
                  cancelAtPeriodEnd?: boolean;
                  periodEnd?: number;
                  periodStart?: number;
                  plan?: string;
                  referenceId?: string;
                  seats?: number;
                  status?: string;
                  stripeCustomerId?: string;
                  stripeSubscriptionId?: string;
                };
                where?: Array<{
                  connector?: "AND" | "OR";
                  field: string;
                  operator?:
                    | "lt"
                    | "lte"
                    | "gt"
                    | "gte"
                    | "eq"
                    | "in"
                    | "ne"
                    | "contains"
                    | "starts_with"
                    | "ends_with";
                  value:
                    | string
                    | number
                    | boolean
                    | Array<string>
                    | Array<number>
                    | null;
                }>;
              }
            | {
                limit?: number;
                model: "rateLimit";
                offset?: number;
                paginationOpts: {
                  cursor: string | null;
                  endCursor?: string | null;
                  id?: number;
                  maximumBytesRead?: number;
                  maximumRowsRead?: number;
                  numItems: number;
                };
                select?: Array<string>;
                sortBy?: { direction: "asc" | "desc"; field: string };
                unique?: boolean;
                update: { count?: number; key?: string; lastRequest?: number };
                where?: Array<{
                  connector?: "AND" | "OR";
                  field: string;
                  operator?:
                    | "lt"
                    | "lte"
                    | "gt"
                    | "gte"
                    | "eq"
                    | "in"
                    | "ne"
                    | "contains"
                    | "starts_with"
                    | "ends_with";
                  value:
                    | string
                    | number
                    | boolean
                    | Array<string>
                    | Array<number>
                    | null;
                }>;
              };
        },
        any
      >;
      updateOne: FunctionReference<
        "mutation",
        "internal",
        {
          input:
            | {
                model: "user";
                update: {
                  banExpires?: number;
                  banReason?: string;
                  banned?: boolean;
                  createdAt?: number;
                  displayUsername?: string;
                  email?: string;
                  emailVerified?: boolean;
                  image?: string;
                  isAnonymous?: boolean;
                  name?: string;
                  phoneNumber?: string;
                  phoneNumberVerified?: boolean;
                  role?: string;
                  stripeCustomerId?: string;
                  twoFactorEnabled?: boolean;
                  updatedAt?: number;
                  userId?: string;
                  username?: string;
                };
                where?: Array<{
                  connector?: "AND" | "OR";
                  field: string;
                  operator?:
                    | "lt"
                    | "lte"
                    | "gt"
                    | "gte"
                    | "eq"
                    | "in"
                    | "ne"
                    | "contains"
                    | "starts_with"
                    | "ends_with";
                  value:
                    | string
                    | number
                    | boolean
                    | Array<string>
                    | Array<number>
                    | null;
                }>;
              }
            | {
                model: "session";
                update: {
                  activeOrganizationId?: string;
                  createdAt?: number;
                  expiresAt?: number;
                  impersonatedBy?: string;
                  ipAddress?: string;
                  token?: string;
                  updatedAt?: number;
                  userAgent?: string;
                  userId?: string;
                };
                where?: Array<{
                  connector?: "AND" | "OR";
                  field: string;
                  operator?:
                    | "lt"
                    | "lte"
                    | "gt"
                    | "gte"
                    | "eq"
                    | "in"
                    | "ne"
                    | "contains"
                    | "starts_with"
                    | "ends_with";
                  value:
                    | string
                    | number
                    | boolean
                    | Array<string>
                    | Array<number>
                    | null;
                }>;
              }
            | {
                model: "account";
                update: {
                  accessToken?: string;
                  accessTokenExpiresAt?: number;
                  accountId?: string;
                  createdAt?: number;
                  idToken?: string;
                  password?: string;
                  providerId?: string;
                  refreshToken?: string;
                  refreshTokenExpiresAt?: number;
                  scope?: string;
                  updatedAt?: number;
                  userId?: string;
                };
                where?: Array<{
                  connector?: "AND" | "OR";
                  field: string;
                  operator?:
                    | "lt"
                    | "lte"
                    | "gt"
                    | "gte"
                    | "eq"
                    | "in"
                    | "ne"
                    | "contains"
                    | "starts_with"
                    | "ends_with";
                  value:
                    | string
                    | number
                    | boolean
                    | Array<string>
                    | Array<number>
                    | null;
                }>;
              }
            | {
                model: "verification";
                update: {
                  createdAt?: number;
                  expiresAt?: number;
                  identifier?: string;
                  updatedAt?: number;
                  value?: string;
                };
                where?: Array<{
                  connector?: "AND" | "OR";
                  field: string;
                  operator?:
                    | "lt"
                    | "lte"
                    | "gt"
                    | "gte"
                    | "eq"
                    | "in"
                    | "ne"
                    | "contains"
                    | "starts_with"
                    | "ends_with";
                  value:
                    | string
                    | number
                    | boolean
                    | Array<string>
                    | Array<number>
                    | null;
                }>;
              }
            | {
                model: "twoFactor";
                update: {
                  backupCodes?: string;
                  secret?: string;
                  userId?: string;
                };
                where?: Array<{
                  connector?: "AND" | "OR";
                  field: string;
                  operator?:
                    | "lt"
                    | "lte"
                    | "gt"
                    | "gte"
                    | "eq"
                    | "in"
                    | "ne"
                    | "contains"
                    | "starts_with"
                    | "ends_with";
                  value:
                    | string
                    | number
                    | boolean
                    | Array<string>
                    | Array<number>
                    | null;
                }>;
              }
            | {
                model: "passkey";
                update: {
                  aaguid?: string;
                  backedUp?: boolean;
                  counter?: number;
                  createdAt?: number;
                  credentialID?: string;
                  deviceType?: string;
                  name?: string;
                  publicKey?: string;
                  transports?: string;
                  userId?: string;
                };
                where?: Array<{
                  connector?: "AND" | "OR";
                  field: string;
                  operator?:
                    | "lt"
                    | "lte"
                    | "gt"
                    | "gte"
                    | "eq"
                    | "in"
                    | "ne"
                    | "contains"
                    | "starts_with"
                    | "ends_with";
                  value:
                    | string
                    | number
                    | boolean
                    | Array<string>
                    | Array<number>
                    | null;
                }>;
              }
            | {
                model: "apikey";
                update: {
                  createdAt?: number;
                  enabled?: boolean;
                  expiresAt?: number;
                  key?: string;
                  lastRefillAt?: number;
                  lastRequest?: number;
                  metadata?: string;
                  name?: string;
                  permissions?: string;
                  prefix?: string;
                  rateLimitEnabled?: boolean;
                  rateLimitMax?: number;
                  rateLimitTimeWindow?: number;
                  refillAmount?: number;
                  refillInterval?: number;
                  remaining?: number;
                  requestCount?: number;
                  start?: string;
                  updatedAt?: number;
                  userId?: string;
                };
                where?: Array<{
                  connector?: "AND" | "OR";
                  field: string;
                  operator?:
                    | "lt"
                    | "lte"
                    | "gt"
                    | "gte"
                    | "eq"
                    | "in"
                    | "ne"
                    | "contains"
                    | "starts_with"
                    | "ends_with";
                  value:
                    | string
                    | number
                    | boolean
                    | Array<string>
                    | Array<number>
                    | null;
                }>;
              }
            | {
                model: "oauthApplication";
                update: {
                  clientId?: string;
                  clientSecret?: string;
                  createdAt?: number;
                  disabled?: boolean;
                  icon?: string;
                  metadata?: string;
                  name?: string;
                  redirectURLs?: string;
                  type?: string;
                  updatedAt?: number;
                  userId?: string;
                };
                where?: Array<{
                  connector?: "AND" | "OR";
                  field: string;
                  operator?:
                    | "lt"
                    | "lte"
                    | "gt"
                    | "gte"
                    | "eq"
                    | "in"
                    | "ne"
                    | "contains"
                    | "starts_with"
                    | "ends_with";
                  value:
                    | string
                    | number
                    | boolean
                    | Array<string>
                    | Array<number>
                    | null;
                }>;
              }
            | {
                model: "oauthAccessToken";
                update: {
                  accessToken?: string;
                  accessTokenExpiresAt?: number;
                  clientId?: string;
                  createdAt?: number;
                  refreshToken?: string;
                  refreshTokenExpiresAt?: number;
                  scopes?: string;
                  updatedAt?: number;
                  userId?: string;
                };
                where?: Array<{
                  connector?: "AND" | "OR";
                  field: string;
                  operator?:
                    | "lt"
                    | "lte"
                    | "gt"
                    | "gte"
                    | "eq"
                    | "in"
                    | "ne"
                    | "contains"
                    | "starts_with"
                    | "ends_with";
                  value:
                    | string
                    | number
                    | boolean
                    | Array<string>
                    | Array<number>
                    | null;
                }>;
              }
            | {
                model: "oauthConsent";
                update: {
                  clientId?: string;
                  consentGiven?: boolean;
                  createdAt?: number;
                  scopes?: string;
                  updatedAt?: number;
                  userId?: string;
                };
                where?: Array<{
                  connector?: "AND" | "OR";
                  field: string;
                  operator?:
                    | "lt"
                    | "lte"
                    | "gt"
                    | "gte"
                    | "eq"
                    | "in"
                    | "ne"
                    | "contains"
                    | "starts_with"
                    | "ends_with";
                  value:
                    | string
                    | number
                    | boolean
                    | Array<string>
                    | Array<number>
                    | null;
                }>;
              }
            | {
                model: "organization";
                update: {
                  createdAt?: number;
                  logo?: string;
                  metadata?: string;
                  name?: string;
                  slug?: string;
                };
                where?: Array<{
                  connector?: "AND" | "OR";
                  field: string;
                  operator?:
                    | "lt"
                    | "lte"
                    | "gt"
                    | "gte"
                    | "eq"
                    | "in"
                    | "ne"
                    | "contains"
                    | "starts_with"
                    | "ends_with";
                  value:
                    | string
                    | number
                    | boolean
                    | Array<string>
                    | Array<number>
                    | null;
                }>;
              }
            | {
                model: "member";
                update: {
                  createdAt?: number;
                  organizationId?: string;
                  role?: string;
                  teamId?: string;
                  userId?: string;
                };
                where?: Array<{
                  connector?: "AND" | "OR";
                  field: string;
                  operator?:
                    | "lt"
                    | "lte"
                    | "gt"
                    | "gte"
                    | "eq"
                    | "in"
                    | "ne"
                    | "contains"
                    | "starts_with"
                    | "ends_with";
                  value:
                    | string
                    | number
                    | boolean
                    | Array<string>
                    | Array<number>
                    | null;
                }>;
              }
            | {
                model: "invitation";
                update: {
                  email?: string;
                  expiresAt?: number;
                  inviterId?: string;
                  organizationId?: string;
                  role?: string;
                  status?: string;
                  teamId?: string;
                };
                where?: Array<{
                  connector?: "AND" | "OR";
                  field: string;
                  operator?:
                    | "lt"
                    | "lte"
                    | "gt"
                    | "gte"
                    | "eq"
                    | "in"
                    | "ne"
                    | "contains"
                    | "starts_with"
                    | "ends_with";
                  value:
                    | string
                    | number
                    | boolean
                    | Array<string>
                    | Array<number>
                    | null;
                }>;
              }
            | {
                model: "team";
                update: {
                  createdAt?: number;
                  name?: string;
                  organizationId?: string;
                  updatedAt?: number;
                };
                where?: Array<{
                  connector?: "AND" | "OR";
                  field: string;
                  operator?:
                    | "lt"
                    | "lte"
                    | "gt"
                    | "gte"
                    | "eq"
                    | "in"
                    | "ne"
                    | "contains"
                    | "starts_with"
                    | "ends_with";
                  value:
                    | string
                    | number
                    | boolean
                    | Array<string>
                    | Array<number>
                    | null;
                }>;
              }
            | {
                model: "ssoProvider";
                update: {
                  domain?: string;
                  issuer?: string;
                  oidcConfig?: string;
                  organizationId?: string;
                  providerId?: string;
                  samlConfig?: string;
                  userId?: string;
                };
                where?: Array<{
                  connector?: "AND" | "OR";
                  field: string;
                  operator?:
                    | "lt"
                    | "lte"
                    | "gt"
                    | "gte"
                    | "eq"
                    | "in"
                    | "ne"
                    | "contains"
                    | "starts_with"
                    | "ends_with";
                  value:
                    | string
                    | number
                    | boolean
                    | Array<string>
                    | Array<number>
                    | null;
                }>;
              }
            | {
                model: "jwks";
                update: {
                  createdAt?: number;
                  privateKey?: string;
                  publicKey?: string;
                };
                where?: Array<{
                  connector?: "AND" | "OR";
                  field: string;
                  operator?:
                    | "lt"
                    | "lte"
                    | "gt"
                    | "gte"
                    | "eq"
                    | "in"
                    | "ne"
                    | "contains"
                    | "starts_with"
                    | "ends_with";
                  value:
                    | string
                    | number
                    | boolean
                    | Array<string>
                    | Array<number>
                    | null;
                }>;
              }
            | {
                model: "subscription";
                update: {
                  cancelAtPeriodEnd?: boolean;
                  periodEnd?: number;
                  periodStart?: number;
                  plan?: string;
                  referenceId?: string;
                  seats?: number;
                  status?: string;
                  stripeCustomerId?: string;
                  stripeSubscriptionId?: string;
                };
                where?: Array<{
                  connector?: "AND" | "OR";
                  field: string;
                  operator?:
                    | "lt"
                    | "lte"
                    | "gt"
                    | "gte"
                    | "eq"
                    | "in"
                    | "ne"
                    | "contains"
                    | "starts_with"
                    | "ends_with";
                  value:
                    | string
                    | number
                    | boolean
                    | Array<string>
                    | Array<number>
                    | null;
                }>;
              }
            | {
                model: "rateLimit";
                update: { count?: number; key?: string; lastRequest?: number };
                where?: Array<{
                  connector?: "AND" | "OR";
                  field: string;
                  operator?:
                    | "lt"
                    | "lte"
                    | "gt"
                    | "gte"
                    | "eq"
                    | "in"
                    | "ne"
                    | "contains"
                    | "starts_with"
                    | "ends_with";
                  value:
                    | string
                    | number
                    | boolean
                    | Array<string>
                    | Array<number>
                    | null;
                }>;
              };
        },
        any
      >;
    };
  };
};
