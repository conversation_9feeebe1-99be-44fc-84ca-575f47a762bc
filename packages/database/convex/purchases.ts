import { query, mutation, internalAction, action, internalMutation } from "./_generated/server";
import { v } from "convex/values";
import { PurchaseType } from "./helpers/enums";
import { api, internal } from "./_generated/api"
import { type Config, config } from "@workspace/config";;

const plans = config.payments.plans as Config["payments"]["plans"];
type PlanId = keyof typeof config.payments.plans;

// Get a purchase by its Convex ID
export const getPurchaseById = query({
  args: { id: v.id("purchases") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.id);
  },
});

// Get all purchases for a given organization
export const getPurchasesByOrganizationId = query({
  args: { organizationId: v.id("organizations") },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("purchases")
      .withIndex("by_organizationId", (q) => q.eq("organizationId", args.organizationId))
      .collect();
  },
});

// Get all purchases for a given user
export const getPurchasesByUserId = query({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("purchases")
      .withIndex("by_userId", (q) => q.eq("userId", args.userId))
      .collect();
  },
});

// Get a purchase by its subscription ID
export const getPurchaseBySubscriptionId = query({
  args: { subscriptionId: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("purchases")
      .withIndex("by_subscriptionId", (q) => q.eq("subscriptionId", args.subscriptionId))
      .first();
  },
});

// Create a new purchase and set the customer ID to the organization or user
export const createPurchaseAndSetCustomerId = action({
  args: {
    organizationId: v.optional(v.id("organizations")),
    userId: v.optional(v.id("users")),
    type: v.union(v.literal(PurchaseType.SUBSCRIPTION), v.literal(PurchaseType.ONE_TIME)),
    customerId: v.string(),
    subscriptionId: v.optional(v.string()),
    productId: v.string(),
    status: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    await ctx.runMutation(internal.purchases.createPurchase, args);
    await ctx.runAction(internal.purchases.setCustomerIdToEntity, {
      customerId: args.customerId,
      organizationId: args.organizationId,
      userId: args.userId,
    });
  },
});

// Create a new purchase
export const createPurchase = internalMutation({
  args: {
    organizationId: v.optional(v.id("organizations")),
    userId: v.optional(v.id("users")),
    type: v.union(v.literal(PurchaseType.SUBSCRIPTION), v.literal(PurchaseType.ONE_TIME)),
    customerId: v.string(),
    subscriptionId: v.optional(v.string()),
    productId: v.string(),
    status: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const purchaseId = await ctx.db.insert("purchases", args);
    return await ctx.db.get(purchaseId);
  },
});

// Update an existing purchase
export const updatePurchase = mutation({
  args: {
    id: v.id("purchases"),
    organizationId: v.optional(v.id("organizations")),
    userId: v.optional(v.id("users")),
    type: v.optional(v.union(v.literal(PurchaseType.SUBSCRIPTION), v.literal(PurchaseType.ONE_TIME))),
    customerId: v.optional(v.string()),
    subscriptionId: v.optional(v.string()),
    productId: v.optional(v.string()),
    status: v.optional(v.string()),
  },
  handler: async (ctx, { id, ...rest }) => {
    await ctx.db.patch(id, rest);
    return await ctx.db.get(id);
  },
});

// Delete a purchase by its subscription ID
export const deletePurchaseBySubscriptionId = mutation({
  args: { subscriptionId: v.string() },
  handler: async (ctx, args) => {
    const purchase = await ctx.db
      .query("purchases")
      .withIndex("by_subscriptionId", (q) => q.eq("subscriptionId", args.subscriptionId))
      .first();

    if (purchase) {
      await ctx.db.delete(purchase._id);
    }
  },
});

// Set the customer ID to the organization or user
export const setCustomerIdToEntity = internalAction({
  args: {
    customerId: v.string(),
    organizationId: v.optional(v.id("organizations")),
    userId: v.optional(v.id("users")),
  },
  handler: async (ctx, { customerId, organizationId, userId }) => {
    if (organizationId) {
      await ctx.runMutation(api.organizations.updateOrganization, {
        id: organizationId,
        paymentsCustomerId: customerId,
      });
    } else if (userId) {
      await ctx.runMutation(api.users.updateUser, {
        id: userId,
        paymentsCustomerId: customerId,
      });
    }
  },
});

// Get the customer ID from the organization or user
export const getCustomerIdFromEntity = query({
  args: { 
    organizationId: v.optional(v.id("organizations")), 
    userId: v.optional(v.id("users")) 
  },
  handler: async (ctx, args) => {
    if (args.organizationId) {
      return (
        (await ctx.db.get(args.organizationId))?.paymentsCustomerId ?? null
      );
    }
    if (args.userId) {
      return (
        (await ctx.db.get(args.userId))?.paymentsCustomerId ?? null
      );
    }
    return null;
  },
});
    
// Get Active Purchase
export const getActivePlanFromPurchases = query({
  args: 
  { 
    purchases: v.array(v.object({
      id: v.id("purchases"),
      type: v.union(v.literal(PurchaseType.SUBSCRIPTION), v.literal(PurchaseType.ONE_TIME)),
      customerId: v.string(),
      subscriptionId: v.optional(v.string()),
      productId: v.string(),
      status: v.optional(v.string()),
    })) 
  },
  handler: async (ctx, args) => {
    const subscriptionPurchase = args.purchases?.find(
      (purchase) => purchase.type === "SUBSCRIPTION",
    );
    
    if (subscriptionPurchase) {
      const plan = Object.entries(plans).find(([_, plan]) =>
        plan.prices?.some(
          (price) => price.productId === subscriptionPurchase.productId,
        ),
      );

      return {
        id: plan?.[0] as PlanId,
        price: plan?.[1].prices?.find(
          (price) => price.productId === subscriptionPurchase.productId,
        ),
        status: subscriptionPurchase.status,
        purchaseId: subscriptionPurchase.id,
      };
    }
    
    const oneTimePurchase = args.purchases?.find(
      (purchase) => purchase.type === "ONE_TIME",
    );
    
    if (oneTimePurchase) {
      const plan = Object.entries(plans).find(([_, plan]) =>
        plan.prices?.some(
          (price) => price.productId === oneTimePurchase.productId,
        ),
      );

      return {
        id: plan?.[0] as PlanId,
        price: plan?.[1].prices?.find(
          (price) => price.productId === oneTimePurchase.productId,
        ),
        status: oneTimePurchase.status,
        purchaseId: oneTimePurchase.id,
      };
    }

    const freePlan = Object.entries(plans).find(([_, plan]) => plan.isFree);
    
    return freePlan
      ? {
          id: freePlan[0] as PlanId,
          status: "active",
          purchaseId: null,
        }
      : null;
  },
});

// Create a purchases helper
export const createPurchaseHelper = query({
  args: {
    purchases: v.array(v.object({
      id: v.id("purchases"),
      type: v.union(v.literal(PurchaseType.SUBSCRIPTION), v.literal(PurchaseType.ONE_TIME)),
      customerId: v.string(),
      subscriptionId: v.optional(v.string()),
      productId: v.string(),
      status: v.optional(v.string()),
    })) 
  },
  handler: async (ctx, args): Promise<{ activePlan: any; hasSubscription: boolean; hasPurchase: boolean; }> => {
    const activePlan = await ctx.runQuery(api.purchases.getActivePlanFromPurchases, {
      purchases: args.purchases,
    });

    const hasSubscription = !!activePlan;
    const hasPurchase = !!args.purchases?.length;

    return { activePlan, hasSubscription, hasPurchase };
  },
})