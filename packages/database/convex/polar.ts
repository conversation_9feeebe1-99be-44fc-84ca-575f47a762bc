import { Polar } from "@convex-dev/polar";
import { api, components } from "./_generated/api";
import { QueryCtx, mutation, query } from "./_generated/server";
import { v } from "convex/values";
import { Id } from "./_generated/dataModel";
import { AiChatRole, SubscriptionPlan, SubscriptionType } from "./helpers/enums";
import { SetSubscriptionSeats } from "./helpers/types";

// User query to use in the Polar component
export const getUserInfo = query({
  args: {},
  handler: async (ctx) => {
    // This would be replaced with an actual auth query,
    // eg., ctx.auth.getUserIdentity() or getAuthUserId(ctx)
    const user = await ctx.db.query("users").first();
    if (!user) {
      throw new Error("User not found");
    }
    return user;
  },
});

export const polar = new Polar(components.polar, {
  products: {
    free: "5fde8344-5fca-4d0b-adeb-2052cddfd9ed",
    premiumMonthly: "9bc5ed5f-2065-40a4-bd1f-e012e448d82f",
    premiumYearly: "db548a6f-ff8c-4969-8f02-5f7301a36e7c",
    premiumPlusMonthly: "9ff9976e-459e-4ebc-8cde-b2ced74f8822",
    premiumPlusYearly: "9ff9976e-459e-4ebc-8cde-b2ced74f8822",
  },
  getUserInfo: async (ctx) => {
    const user: { _id: Id<"users">; email: string } = await ctx.runQuery(
      api.polar.getUserInfo
    );
    return {
      userId: user._id,
      email: user.email,
    };
  },

  // These can be configured in code or via environment variables
  // Uncomment and replace with actual values to configure in code:
  // organizationToken: "your_organization_token", // Or use POLAR_ORGANIZATION_TOKEN env var
  // webhookSecret: "your_webhook_secret", // Or use POLAR_WEBHOOK_SECRET env var
  // server: "sandbox", // "sandbox" or "production", falls back to POLAR_SERVER env var
});

export const MAX_FREE_CHATS = 3;
export const MAX_PREMIUM_CHATS = 6;

export const {
  // If you configure your products by key in the Polar constructor,
  // this query provides a keyed object of the products.
  getConfiguredProducts,

  // Lists all non-archived products, useful if you don't configure products by key.
  listAllProducts,

  // Generates a checkout link for the given product IDs.
  generateCheckoutLink,

  // Generates a customer portal URL for the current user.
  generateCustomerPortalUrl,

  // Changes the current subscription to the given product ID.
  changeCurrentSubscription,

  // Cancels the current subscription.
  cancelCurrentSubscription,
} = polar.api();

// In a real app you'll set up authentication, we just use a
// fake user for the example.
const currentUser = async (ctx: QueryCtx) => {
  const user = await ctx.db.query("users").first();
  if (!user) {
    throw new Error("No user found");
  }
  const subscription = await polar.getCurrentSubscription(ctx, {
    userId: user._id,
  });

  const productKey = subscription?.productKey;

  const isPremium =
    productKey === "premiumMonthly" || productKey === "premiumYearly";
  const isPremiumPlus =
    productKey === "premiumPlusMonthly" || productKey === "premiumPlusYearly";

  return {
    ...user,
    isFree: !isPremium && !isPremiumPlus,
    isPremium,
    isPremiumPlus,
    subscription,
    maxChats: isPremiumPlus
      ? undefined
      : isPremium
        ? MAX_PREMIUM_CHATS
        : MAX_FREE_CHATS,
  };
};

// Query that returns our pseudo user.
export const getCurrentUser = query({
  handler: async (ctx) => {
    return currentUser(ctx);
  },
});

// Authorize a chat
export const authorizeChat = async (ctx: QueryCtx, chatId: Id<"aiChats">) => {
  const user = await currentUser(ctx);
  const chat = await ctx.db.get(chatId);
  if (!chat || chat.userId !== user._id) {
    throw new Error("Chat not found");
  }
};

// List all chats
export const listChats = query({
  handler: async (ctx) => {
    const user = await currentUser(ctx);
    return ctx.db
      .query("aiChats")
      .withIndex("by_userId", (q) => q.eq("userId", user._id))
      .collect();
  },
});

// Insert a chat
export const insertChat = mutation({
  args: {
    text: v.string(),
  },
  handler: async (ctx, args) => {
    const user = await currentUser(ctx);
    const chatCount = (
      await ctx.db
        .query("aiChats")
        .withIndex("by_userId", (q) => q.eq("userId", user._id))
        .collect()
    ).length;
    const productKey = user.subscription?.productKey;
    if (!productKey && chatCount >= MAX_FREE_CHATS) {
      throw new Error("Reached maximum number of todos for free plan");
    }
    if (
      (productKey === "premiumMonthly" || productKey === "premiumYearly") ||
      (productKey === "premiumPlusMonthly" || productKey === "premiumPlusYearly") &&
      chatCount >= MAX_PREMIUM_CHATS
    ) {
      throw new Error("Reached maximum number of todos for premium plan");
    }
    await ctx.db.insert("aiChats", {
      userId: user._id,
      messages: [
        {
          role: AiChatRole.USER,
          content: args.text,
        },
      ],
    });
  },
});

// Update a chat
export const updateChatText = mutation({
  args: {
    chatId: v.id("aiChats"),
    text: v.string(),
  },
  handler: async (ctx, args) => {
    await authorizeChat(ctx, args.chatId);
    await ctx.db.patch(args.chatId, { messages: [{ role: AiChatRole.USER, content: args.text }] });
  },
});

// Delete a chat
export const deleteChat = mutation({
  args: {
    chatId: v.id("aiChats"),
  },
  handler: async (ctx, args) => {
    await authorizeChat(ctx, args.chatId);
    await ctx.db.delete(args.chatId);
  },
});

// Set subscription seats
export const setSubscriptionSeats: SetSubscriptionSeats = async () => {
  // TODO: implement seat based subscription
  throw new Error("Not implemented");
};
