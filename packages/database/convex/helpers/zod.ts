import { z } from "zod"
import { PurchaseType, AiChatRole } from "./enums"

export const PurchaseSchema = z.object({
  type: z.enum([PurchaseType.SUBSCRIPTION, PurchaseType.ONE_TIME]),
  id: z.cuid(),
  organizationId: z.string().nullable(),
  userId: z.string().nullable(),
  customerId: z.string(),
  subscriptionId: z.string().nullable(),
  productId: z.string(),
  status: z.string().nullable(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type Purchase = z.infer<typeof PurchaseSchema>

export const AiChatMessageSchema = z.object({
  role: z.enum([AiChatRole.USER, AiChatRole.ASSISTANT]),
  content: z.string(),
})

export const AiChatSchema = z.object({
  _id: z.string(),
  _creationTime: z.number(),
  organizationId: z.string().nullable().optional(),
  userId: z.string().nullable().optional(),
  title: z.string().optional(),
  messages: z.array(AiChatMessageSchema),
}) as z.ZodTypeAny

export type AiChat = z.infer<typeof AiChatSchema>
export type AiChatMessage = z.infer<typeof AiChatMessageSchema>
