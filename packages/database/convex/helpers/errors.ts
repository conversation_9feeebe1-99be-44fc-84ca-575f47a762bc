import { APIError as BetterA<PERSON><PERSON>IError } from "better-auth/api";

export const ERROR_CODES = {
	BAD_REQUEST: "BAD_REQUEST",
	UNAUTHORIZED: "UNAUTHORIZED",
	NOT_FOUND: "NOT_FOUND",
	INTERNAL_SERVER_ERROR: "INTERNAL_SERVER_ERROR",
	INVALID_INVITATION: "BAD_REQUEST",
} as const;

export const ERROR_MESSAGES = {
	BAD_REQUEST: "Bad request",
	UNAUTHORIZED: "Unauthorized",
	NOT_FOUND: "Not found",
	INTERNAL_SERVER_ERROR: "Internal server error",
	INVALID_INVITATION: "No invitation found for this email",
	ORGANIZATION_MEMBERSHIP: "User is not a member of this organization",
	NO_AVAILABLE_SLUG: "No available slug found",
} as const;

export type ErrorCode = keyof typeof ERROR_CODES;

export class APIError extends BetterAuthAPIError {
	constructor(code: ErrorCode, message: string) {
		super(ERROR_CODES[code], { message });
	}
}