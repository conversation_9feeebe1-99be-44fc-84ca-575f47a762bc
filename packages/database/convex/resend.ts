import { api, internal } from "./_generated/api";
import { components } from "./_generated/api";
import { Resend } from "@convex-dev/resend";
import { internalMutation, action, internalAction } from "./_generated/server";
import { v } from "convex/values";

export const resend: Resend = new Resend(components.resend, {});

// Basic email sending function for internal use
export const sendEmail = internalMutation({
  args: {
    to: v.string(),
    from: v.optional(v.string()),
    subject: v.string(),
    html: v.string(),
    text: v.optional(v.string()),
  },
  handler: async (ctx, { to, from, subject, html, text }) => {
    await resend.sendEmail(ctx, {
      from: from || "StartWithConvex <<EMAIL>>",
      to,
      subject,
      html,
      ...(text && { text }),
    });
  },
});

// Email template rendering and sending
export const sendTemplateEmail = internalAction({
  args: {
    to: v.string(),
    templateId: v.string(),
    context: v.any(),
    from: v.optional(v.string()),
  },
  handler: async (ctx, { to, templateId, context, from }) => {
    const { html, text, subject } = await renderEmailTemplate(templateId, context);
    
    await ctx.runMutation(internal.resend.sendEmail, {
      to,
      from,
      subject,
      html,
      text,
    });
  },
});

// Advanced template email with custom HTML (for React Email integration)
export const sendCustomTemplateEmail = internalMutation({
  args: {
    to: v.string(),
    from: v.optional(v.string()),
    subject: v.string(),
    html: v.string(),
    text: v.optional(v.string()),
  },
  handler: async (ctx, { to, from, subject, html, text }) => {
    await resend.sendEmail(ctx, {
      from: from || "StartWithConvex <<EMAIL>>",
      to,
      subject,
      html,
      ...(text && { text }),
    });
  },
});

// Local template rendering function
async function renderEmailTemplate(templateId: string, context: any) {
  const templates = {
    magicLink: (ctx: { magicLink: string }) => ({
      subject: "Your magic link",
      html: `<div style="font-family: Arial, sans-serif; padding: 20px;">
        <h2>Sign in to StartWithConvex</h2>
        <p>Click the button below to sign in to your account:</p>
        <a href="${ctx.magicLink}" style="background: #007cba; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;">Sign In</a>
        <p style="margin-top: 20px; color: #666;">If the button doesn't work, copy and paste this link: ${ctx.magicLink}</p>
      </div>`,
      text: `Sign in using this link: ${ctx.magicLink}`,
    }),
    emailVerification: (ctx: { verificationLink: string }) => ({
      subject: "Verify your email address",
      html: `<div style="font-family: Arial, sans-serif; padding: 20px;">
        <h2>Verify your email</h2>
        <p>Please verify your email address by clicking the button below:</p>
        <a href="${ctx.verificationLink}" style="background: #007cba; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;">Verify Email</a>
        <p style="margin-top: 20px; color: #666;">If the button doesn't work, copy and paste this link: ${ctx.verificationLink}</p>
      </div>`,
      text: `Verify your email: ${ctx.verificationLink}`,
    }),
    organizationInvitation: (ctx: { organizationName: string; inviterName: string; invitationLink: string }) => ({
      subject: `Invitation to join ${ctx.organizationName}`,
      html: `<div style="font-family: Arial, sans-serif; padding: 20px;">
        <h2>You're invited!</h2>
        <p>${ctx.inviterName} has invited you to join <strong>${ctx.organizationName}</strong>.</p>
        <a href="${ctx.invitationLink}" style="background: #007cba; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;">Accept Invitation</a>
        <p style="margin-top: 20px; color: #666;">If the button doesn't work, copy and paste this link: ${ctx.invitationLink}</p>
      </div>`,
      text: `${ctx.inviterName} invited you to join ${ctx.organizationName}. Accept: ${ctx.invitationLink}`,
    }),
    forgotPassword: (ctx: { resetLink: string }) => ({
      subject: "Reset your password",
      html: `<div style="font-family: Arial, sans-serif; padding: 20px;">
        <h2>Reset your password</h2>
        <p>Click the button below to reset your password:</p>
        <a href="${ctx.resetLink}" style="background: #007cba; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;">Reset Password</a>
        <p style="margin-top: 20px; color: #666;">If the button doesn't work, copy and paste this link: ${ctx.resetLink}</p>
        <p style="color: #999;">If you didn't request this, you can safely ignore this email.</p>
      </div>`,
      text: `Reset your password: ${ctx.resetLink}`,
    }),
    newUser: (ctx: { userName: string }) => ({
      subject: "Welcome to StartWithConvex!",
      html: `<div style="font-family: Arial, sans-serif; padding: 20px;">
        <h2>Welcome ${ctx.userName}!</h2>
        <p>Thanks for joining StartWithConvex. We're excited to have you on board!</p>
        <p>Get started by exploring your dashboard and setting up your first project.</p>
      </div>`,
      text: `Welcome ${ctx.userName}! Thanks for joining StartWithConvex.`,
    }),
    newsletterSignup: (ctx: { userName?: string }) => ({
      subject: "Welcome to our newsletter!",
      html: `<div style="font-family: Arial, sans-serif; padding: 20px;">
        <h2>Thanks for subscribing!</h2>
        <p>You'll now receive our latest updates and news.</p>
      </div>`,
      text: "Thanks for subscribing to our newsletter!",
    }),
  };

  const template = templates[templateId as keyof typeof templates];
  if (!template) {
    throw new Error(`Template ${templateId} not found`);
  }
  
  return template(context);
}

// Test email function
export const sendTestEmail = internalMutation({
  handler: async (ctx) => {
    await resend.sendEmail(ctx, {
      from: "StartWithConvex <<EMAIL>>",
      to: "<EMAIL>",
      subject: "Hi there from Convex!",
      html: "<p>This is a test email from Convex Resend integration</p>",
    });
  },
});

// Convenience functions for common email types
export const sendMagicLinkEmail = internalAction({
  args: {
    to: v.string(),
    magicLink: v.string(),
  },
  handler: async (ctx, { to, magicLink }) => {
    await ctx.runAction(internal.resend.sendTemplateEmail, {
      to,
      templateId: "magicLink",
      context: { magicLink },
    });
  },
});

export const sendEmailVerification = internalAction({
  args: {
    to: v.string(),
    verificationLink: v.string(),
  },
  handler: async (ctx, { to, verificationLink }) => {
    await ctx.runAction(internal.resend.sendTemplateEmail, {
      to,
      templateId: "emailVerification", 
      context: { verificationLink },
    });
  },
});

export const sendOrganizationInvitation = internalAction({
  args: {
    to: v.string(),
    organizationName: v.string(),
    inviterName: v.string(),
    invitationLink: v.string(),
  },
  handler: async (ctx, { to, organizationName, inviterName, invitationLink }) => {
    await ctx.runAction(internal.resend.sendTemplateEmail, {
      to,
      templateId: "organizationInvitation",
      context: { organizationName, inviterName, invitationLink },
    });
  },
});

export const sendForgotPasswordEmail = internalAction({
  args: {
    to: v.string(),
    resetLink: v.string(),
  },
  handler: async (ctx, { to, resetLink }) => {
    await ctx.runAction(internal.resend.sendTemplateEmail, {
      to,
      templateId: "forgotPassword",
      context: { resetLink },
    });
  },
});

export const sendNewUserWelcome = internalAction({
  args: {
    to: v.string(),
    userName: v.string(),
  },
  handler: async (ctx, { to, userName }) => {
    await ctx.runAction(internal.resend.sendTemplateEmail, {
      to,
      templateId: "newUser",
      context: { userName },
    });
  },
});
