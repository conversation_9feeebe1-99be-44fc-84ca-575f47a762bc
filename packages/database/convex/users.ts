import { query, mutation, internalMutation } from "./_generated/server";
import { v } from "convex/values";

// Get all users
export const getUsers = query({
  args: {
    limit: v.optional(v.number()),
    offset: v.optional(v.number()),
    search: v.optional(v.string()),
  },
  handler: async (ctx, { limit = 10, offset = 0, search }) => {
    const users = await ctx.db.query("users").fullTableScan().collect();

    const filtered = search
      ? users.filter((user) =>
          user.name.toLowerCase().includes(search.toLowerCase())
        )
      : users;

    return filtered.slice(offset, offset + limit);
  },
});

// Count all users
export const countAllUsers = query({
  handler: async (ctx) => {
    return (await ctx.db.query("users").collect()).length;
  },
});

// Get a user by ID
export const getUserById = query({
  args: { id: v.id("users") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.id);
  },
});

// Get a user by email
export const getUserByEmail = query({
  args: { email: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", args.email))
      .first();
  },
});

// Create a new user
export const createUser = mutation({
  args: {
    email: v.string(),
    name: v.string(),
    role: v.union(v.literal("admin"), v.literal("user")),
    emailVerified: v.boolean(),
    onboardingComplete: v.boolean(),
  },
  handler: async (ctx, args) => {
    return await ctx.db.insert("users", args);
  },
});

// Update a user
export const updateUser = mutation({
  args: {
    id: v.id("users"),
    name: v.optional(v.string()),
    email: v.optional(v.string()),
    emailVerified: v.optional(v.boolean()),
    image: v.optional(v.string()),
    username: v.optional(v.string()),
    role: v.optional(v.string()),
    banned: v.optional(v.boolean()),
    banReason: v.optional(v.string()),
    banExpires: v.optional(v.number()),
    onboardingComplete: v.optional(v.boolean()),
    paymentsCustomerId: v.optional(v.string()),
    twoFactorEnabled: v.optional(v.boolean()),
  },
  handler: async (ctx, { id, ...rest }) => {
    await ctx.db.patch(id, rest);
  },
});

// Update a user's payments customer ID
export const updateUserPaymentsId = internalMutation({
  args: { id: v.id("users"), paymentsCustomerId: v.string() },
  handler: async (ctx, { id, paymentsCustomerId }) => {
    await ctx.db.patch(id, { paymentsCustomerId });
  },
});

export const getUserAuthMethods = query({
  args: {
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    const accounts = await ctx.db
      .query("accounts")
      .withIndex("by_userId", (q) => q.eq("userId", args.userId))
      .collect();

    return accounts.map(account => ({
      providerId: account.providerId,
      hasPassword: !!account.password,
      lastUsed: account.accessTokenExpiresAt || 0
    }));
  },
});

// Get an account by ID
export const getAccountById = query({
  args: { id: v.id("accounts") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.id);
  },
});

// Create a new account
export const createUserAccount = mutation({
  args: {
    userId: v.id("users"),
    providerId: v.string(),
    accountId: v.string(),
    hashedPassword: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    return await ctx.db.insert("accounts", args);
  },
});
