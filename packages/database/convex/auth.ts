import { AuthFunctions, BetterAuth } from "@convex-dev/better-auth";
import { components, internal } from "./_generated/api";
import { query } from "./_generated/server";
import { Id, DataModel } from "./_generated/dataModel";
import { asyncMap } from "convex-helpers";
import type { Session } from "./auth/types";

// Typesafe way to pass Convex functions defined in this file
const authFunctions: AuthFunctions = internal.auth;

// Initialize the component
export const betterAuthComponent = new BetterAuth(components.betterAuth, {
  authFunctions,
  verbose: true,
});

// These are required named exports
export const { createUser, deleteUser, updateUser, createSession } =
  betterAuthComponent.createAuthFunctions<DataModel>({
    onCreateUser: async (ctx, user) => {
      // Example: copy the user's email to the application users table.
      // We'll use onUpdateUser to keep it synced.
      const userData: any = {
        name: user.name || "",
        email: user.email,
        emailVerified: user.emailVerified || false,
        onboardingComplete: false,
      };

      if (user.image) userData.image = user.image;
      if (user.username) userData.username = user.username;
      if (user.role) userData.role = user.role;

      const userId = await ctx.db.insert("users", userData);
      return userId;
    },

    onDeleteUser: async (ctx, userId) => {
      // Delete the user's data if the user is being deleted
      const aiChats = await ctx.db
        .query("aiChats")
        .withIndex("by_userId", (q) => q.eq("userId", userId as Id<"users">))
        .collect();
      await asyncMap(aiChats, async (chat) => {
        await ctx.db.delete(chat._id);
      });

      await ctx.db.delete(userId as Id<"users">);
    },

    onUpdateUser: async (ctx, user) => {
      // Keep the user's data synced
      const userId = user.userId as Id<"users">;
      const updateData: any = {
        name: user.name,
        email: user.email,
        emailVerified: user.emailVerified,
      };

      if (user.image !== undefined) updateData.image = user.image;
      if (user.username !== undefined) updateData.username = user.username;

      await ctx.db.patch(userId, updateData);
    },
  });

export const getCurrentUser = query({
  args: {},
  handler: async (ctx) => {
    const userMetadata = await betterAuthComponent.getAuthUser(ctx);
    if (!userMetadata) {
      return null;
    }

    const user = await ctx.db.get(userMetadata.userId as Id<"users">);
    return {
      ...user,
      ...userMetadata,
    };
  },
});

export { type Session };