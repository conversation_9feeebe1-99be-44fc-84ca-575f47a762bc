import { BetterAuth } from "@convex-dev/better-auth";
import { api, components } from "./_generated/api";
import { query } from "./_generated/server";
import { Id } from "./_generated/dataModel";
import { config } from "@workspace/config";
import { sendEmail } from "@workspace/mail";
import { getBaseUrl } from "@workspace/utils";
import { betterAuth } from "better-auth";
import { convexAdapter } from "@convex-dev/better-auth";
import { convex, crossDomain } from "@convex-dev/better-auth/plugins";
import { convex as convexClient } from "./helpers/client";
import { admin, magicLink, openAPI, twoFactor, username, organization } from "better-auth/plugins";
import type { GenericCtx } from "./_generated/server";
import { invitationOnly } from "./auth/plugins/invitationOnly";
import { passkey } from "better-auth/plugins/passkey";
import type { Session } from "./auth/types";

export const betterAuthComponent: any = new (BetterAuth as any)(components.betterAuth, {
  verbose: true,
});

export const { createUser, deleteUser, updateUser, createSession, isAuthenticated } =
  betterAuthComponent.createAuthFunctions({
    onCreateUser: async (ctx: any, user: any) => {
      console.log("onCreateUser called with:", user);
      const userData: any = {
        name: user.name || "",
        email: user.email,
        emailVerified: user.emailVerified || false,
        onboardingComplete: false,
      };
      
      if (user.image) userData.image = user.image;
      if (user.username) userData.username = user.username;
      
      console.log("Attempting to insert user with data:", userData);
      const userId = await ctx.db.insert("users", userData);
      console.log("User created with ID:", userId);

      return userId;
    },

    onDeleteUser: async (ctx: any, userId: any) => {
      await ctx.db.delete(userId as Id<"users">);
    },

    onUpdateUser: async (ctx: any, user: any) => {
      const userId = user.userId as Id<"users">;
      const updateData: any = {
        name: user.name,
        email: user.email,
        emailVerified: user.emailVerified,
      };
      
      if (user.image !== undefined) updateData.image = user.image;
      if (user.username !== undefined) updateData.username = user.username;
      
      await ctx.db.patch(userId, updateData);
    },
  });

export const getCurrentUser = query({
  args: {},
  handler: async (ctx) => {
    const userMetadata = await betterAuthComponent.getAuthUser(ctx);
    if (!userMetadata) {
      return null;
    }

    const user = await ctx.db.get(userMetadata.userId as Id<"users">);
    return {
      ...user,
      ...userMetadata,
    };
  },
});

// Shared auth configuration
const appUrl = getBaseUrl();

const sharedAuthConfig: any = {
  baseURL: appUrl,
  trustedOrigins: [appUrl],
  appName: config.appName,
  user: {
    additionalFields: {
      onboardingComplete: {
        type: "boolean",
        required: false,
      },
      role: {
        type: "string",
        required: false,
      },
    },
  },

  emailAndPassword: {
    enabled: true,
    requireEmailVerification: config.auth.enableSignup,
    autoSignIn: !config.auth.enableSignup,
    sendResetPassword: async ({ user, url }: any, request?: any) => {
      await sendEmail({
        to: user.email,
        templateId: "forgotPassword",
        context: {
          url,
          name: user.name,
        },
      });
    },
  },

  account: {
    accountLinking: {
      enabled: true,
      trustedProviders: ["google", "github"],
    },
  },

  socialProviders: {
    google: {
      clientId: process.env.GOOGLE_CLIENT_ID as string,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET as string,
      scope: ["email", "profile"],
    },
    github: {
      clientId: process.env.GITHUB_CLIENT_ID as string,
      clientSecret: process.env.GITHUB_CLIENT_SECRET as string,
      scope: ["user:email"],
    },
  },

  session: {
    expiresIn: config.auth.sessionCookieMaxAge,
    freshAge: 0,
  },

  plugins: [
    convex(),
    crossDomain({
      siteUrl: appUrl,
    }),
    admin(),
    username(),
    passkey(),
    magicLink({
      disableSignUp: true,
      sendMagicLink: async ({ email, url }: any, request: any) => {
        await sendEmail({
          to: email,
          templateId: "magicLink",
          context: {
            url,
          },
        });
      },
    }),
    organization({
      sendInvitationEmail: async (
        { email, id, organization }: any,
        request: any,
      ) => {
        try {
          const url = new URL("/auth/signup", getBaseUrl());

          url.searchParams.set("invitationId", id);
          url.searchParams.set("email", email);

          await sendEmail({
            to: email,
            templateId: "organizationInvitation",
            context: {
              organizationName: organization.name,
              url: url.toString(),
            },
          });
        } catch (error) {
          console.error("Error sending invitation email:", error);
        }
      },
    }),
    openAPI(),
    invitationOnly(),
    twoFactor(),
  ],
};

export function createAuth(ctx: GenericCtx): any {
  return betterAuth({
    ...sharedAuthConfig,
    database: convexAdapter(ctx, betterAuthComponent),
  });
}

// Export auth instance for server-side usage  
export const auth: any = betterAuth(sharedAuthConfig);

export { type Session }; 