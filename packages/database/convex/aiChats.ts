import { query, mutation } from "./_generated/server";
import { v } from "convex/values";
import { AiChatRole } from "./helpers/enums";

// Get all AI chats for a given user
export const getAiChatsByUserId = query({
  args: {
    limit: v.optional(v.number()),
    offset: v.optional(v.number()),
    userId: v.id("users"),
  },
  handler: async (ctx, { limit = 10, offset = 0, userId }) => {
    return await ctx.db
      .query("aiChats")
      .withIndex("by_userId", (q) => q.eq("userId", userId))
      .order("desc")
      .take(limit)
  },
});

// Get all AI chats for a given organization
export const getAiChatsByOrganizationId = query({
  args: {
    limit: v.optional(v.number()),
    offset: v.optional(v.number()),
    organizationId: v.id("organizations"),
  },
  handler: async (ctx, { limit = 10, offset = 0, organizationId }) => {
    return await ctx.db
      .query("aiChats")
      .withIndex("by_organizationId", (q) => q.eq("organizationId", organizationId))
      .order("desc")
      .take(limit)
  },
});

// Get an AI chat by its ID
export const getAiChatById = query({
  args: { id: v.id("aiChats") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.id);
  },
});

// Create a new AI chat
export const createAiChat = mutation({
  args: {
    organizationId: v.optional(v.id("organizations")),
    userId: v.id("users"),
    title: v.optional(v.string()),
    messages: v.optional(
      v.array(
        v.object({
          role: v.union(v.literal(AiChatRole.USER), v.literal(AiChatRole.ASSISTANT)),
          content: v.string(),
        })
      )
    ),
  },
  handler: async (ctx, args) => {
    return await ctx.db.insert("aiChats", {
      ...args,
      messages: args.messages || [],
    });
  },
});

// Update an existing AI chat
export const updateAiChat = mutation({
  args: {
    id: v.id("aiChats"),
    title: v.optional(v.string()),
    messages: v.optional(
      v.array(
        v.object({
          role: v.union(v.literal(AiChatRole.USER), v.literal(AiChatRole.ASSISTANT)),
          content: v.string(),
        })
      )
    ),
  },
  handler: async (ctx, { id, ...rest }) => {
    await ctx.db.patch(id, rest);
  },
});

// Delete an AI chat
export const deleteAiChat = mutation({
  args: { id: v.id("aiChats") },
  handler: async (ctx, args) => {
    await ctx.db.delete(args.id);
  },
});
