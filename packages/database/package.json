{"name": "@workspace/database", "version": "1.0.0", "description": "Database package using Convex", "main": "index.js", "scripts": {"dev": "convex dev", "build": "convex codegen", "deploy": "convex deploy", "seed": "convex run seed"}, "dependencies": {"@convex-dev/better-auth": "0.7.7", "@convex-dev/polar": "^0.5.0", "@convex-dev/resend": "^0.1.4", "@polar-sh/sdk": "^0.34.5", "@workspace/config": "workspace:*", "@workspace/mail": "workspace:*", "@workspace/utils": "workspace:*", "better-auth": "1.2.12", "convex": "^1.25.4"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@types/node": "^20", "@workspace/typescript-config": "workspace:*", "typescript": "^5.8.3"}, "keywords": ["convex", "database", "backend"], "author": "", "license": "ISC"}