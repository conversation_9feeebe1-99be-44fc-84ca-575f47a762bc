{"dependencies": {"@aws-sdk/client-s3": "3.437.0", "@aws-sdk/s3-request-presigner": "3.437.0", "@edgestore/react": "^0.5.2", "@edgestore/server": "^0.5.2", "@workspace/config": "workspace:*", "zod": "^3.25.67"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@workspace/typescript-config": "workspace:*", "@types/node": "^22.13.10"}, "main": "./index.ts", "name": "@workspace/storage", "scripts": {"type-check": "tsc --noEmit"}, "types": "./**/.ts", "version": "0.0.0"}