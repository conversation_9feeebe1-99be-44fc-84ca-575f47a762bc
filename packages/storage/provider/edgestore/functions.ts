import { FileCategory } from "../../types";
import { config } from "@workspace/config";
import { randomUUID } from "crypto";

export const getFileCategoryFromMimeType = (mimeType: string): FileCategory => {
  if (mimeType.startsWith('image/')) {
    return 'images';
  }
  if (mimeType.startsWith('video/')) {
    return 'videos';
  }
  if (mimeType.startsWith('audio/')) {
    return 'audio';
  }

  // Document types
  const documentTypes = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'text/plain',
    'text/csv',
    'application/rtf',
  ];

  if (documentTypes.includes(mimeType)) {
    return 'documents';
  }

  // Archive types
  const archiveTypes = [
    'application/zip',
    'application/x-rar-compressed',
    'application/x-7z-compressed',
    'application/x-tar',
    'application/gzip',
  ];

  if (archiveTypes.includes(mimeType)) {
    return 'archives';
  }

  return 'other';
};

export const getFileExtension = (filename: string): string => {
  const lastDotIndex = filename.lastIndexOf('.');
  return lastDotIndex !== -1 ? filename.slice(lastDotIndex + 1).toLowerCase() : '';
};

export const getFileTypeFromExtension = (extension: string): FileCategory => {
  const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg', 'bmp', 'tiff'];
  const videoExtensions = ['mp4', 'mpeg', 'mov', 'avi', 'webm', 'wmv'];
  const audioExtensions = ['mp3', 'wav', 'aac', 'ogg', 'webm'];
  const documentExtensions = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'csv', 'rtf'];
  const archiveExtensions = ['zip', 'rar', '7z', 'tar', 'gz'];

  if (imageExtensions.includes(extension)) return 'images';
  if (videoExtensions.includes(extension)) return 'videos';
  if (audioExtensions.includes(extension)) return 'audio';
  if (documentExtensions.includes(extension)) return 'documents';
  if (archiveExtensions.includes(extension)) return 'archives';

  return 'other';
};

// Backend EdgeStore utilities

/**
 * Create upload URLs for EdgeStore integration
 * @param bucket The storage bucket name (documents, images, videos, etc.)
 * @param filename The original filename
 * @returns Object containing uploadUrl and accessUrl
 * 
 * @example
 * ```typescript
 * const { uploadUrl, accessUrl } = await createUploadUrl('images', 'photo.jpg');
 * // Use uploadUrl for uploading the file
 * // Use accessUrl to reference the uploaded file
 * ```
 */
export async function createUploadUrl(bucket: string, filename: string): Promise<{
  uploadUrl: string;
  accessUrl: string;
}> {
  try {
    // Generate a unique filename with timestamp and UUID for better uniqueness
    const timestamp = Date.now();
    const uuid = randomUUID();
    const extension = getFileExtension(filename);
    const nameWithoutExtension = filename.substring(0, filename.lastIndexOf('.')) || filename;
    const uniqueFilename = `${timestamp}-${uuid}-${nameWithoutExtension}${extension ? `.${extension}` : ''}`;
    
    // Create proper EdgeStore URLs
    const accessUrl = `${config.storage.edgeStore.baseUrl}/files/${bucket}/${uniqueFilename}`;
    const uploadUrl = `${config.storage.edgeStore.baseUrl}/api/upload/${bucket}`;
    
    return {
      uploadUrl,
      accessUrl,
    };
  } catch (error) {
    console.error('Error creating EdgeStore upload URL:', error);
    throw new Error('Failed to create upload URL');
  }
}

// Helper function to validate file against bucket restrictions
export function validateFileForBucket(bucket: string, filename: string, contentType?: string): {
  isValid: boolean;
  error?: string;
} {
  const extension = getFileExtension(filename);
  const categoryFromExtension = getFileTypeFromExtension(extension);
  const categoryFromMimeType = contentType ? getFileCategoryFromMimeType(contentType) : null;
  
  // Check if file type matches the bucket
  if (bucket !== 'other' && bucket !== categoryFromExtension && bucket !== categoryFromMimeType) {
    return {
      isValid: false,
      error: `File type does not match bucket '${bucket}'. Expected ${bucket} file.`
    };
  }
  
  return { isValid: true };
}

/**
 * Frontend usage example:
 * 
 * ```typescript
 * // 1. Get signed upload URL from your API
 * const response = await fetch('/api/uploads/signed-upload-url', {
 *   method: 'POST',
 *   headers: { 'Content-Type': 'application/json' },
 *   body: JSON.stringify({
 *     filename: file.name,
 *     contentType: file.type,
 *     // bucket is optional - will be auto-determined from contentType
 *   })
 * });
 * 
 * const { uploadUrl, accessUrl, bucket } = await response.json();
 * 
 * // 2. Upload file to EdgeStore
 * const formData = new FormData();
 * formData.append('file', file);
 * 
 * await fetch(uploadUrl, {
 *   method: 'POST',
 *   body: formData,
 * });
 * 
 * // 3. Use accessUrl to reference the uploaded file
 * console.log('File uploaded to:', accessUrl);
 * ```
 */