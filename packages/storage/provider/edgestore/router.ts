import { initEdgeStore } from "@edgestore/server";
import { config } from "@workspace/config";

const es = initEdgeStore.create();

export const edgeStoreRouter = es.router({
  publicFiles: es.fileBucket().beforeDelete(() => {
    return true;
  }),
  avatars: es.imageBucket({
    maxSize: 1024 * 1024 * 4, // 4MB
    accept: ['image/jpeg', 'image/png'],
  }).beforeDelete(() => {
    return true;
  }),
  // File management buckets
  documents: es.fileBucket({
    maxSize: 1024 * 1024 * 50, // 50MB
    accept: [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-powerpoint',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'text/plain',
      'text/csv',
      'application/rtf',
    ],
  }).beforeDelete(() => {
    return true;
  }),
  images: es.imageBucket({
    maxSize: 1024 * 1024 * 10, // 10MB
    accept: [
      'image/jpeg',
      'image/jpg',
      'image/png',
      'image/gif',
      'image/webp',
      'image/svg+xml',
      'image/bmp',
      'image/tiff',
    ],
  }).beforeDelete(() => {
    return true;
  }),
  videos: es.fileBucket({
    maxSize: 1024 * 1024 * 500, // 500MB
    accept: [
      'video/mp4',
      'video/mpeg',
      'video/quicktime',
      'video/x-msvideo',
      'video/webm',
      'video/x-ms-wmv',
    ],
  }).beforeDelete(() => {
    return true;
  }),
  audio: es.fileBucket({
    maxSize: 1024 * 1024 * 100, // 100MB
    accept: [
      'audio/mpeg',
      'audio/wav',
      'audio/mp3',
      'audio/mp4',
      'audio/aac',
      'audio/ogg',
      'audio/webm',
    ],
  }).beforeDelete(() => {
    return true;
  }),
  archives: es.fileBucket({
    maxSize: 1024 * 1024 * 100, // 100MB
    accept: [
      'application/zip',
      'application/x-rar-compressed',
      'application/x-7z-compressed',
      'application/x-tar',
      'application/gzip',
    ],
  }).beforeDelete(() => {
    return true;
  }),
  other: es.fileBucket({
    maxSize: 1024 * 1024 * 25, // 25MB
    // Accept any file type not covered by other buckets
  }).beforeDelete(() => {
    return true;
  }),
});

export function createEdgeStoreHonoHandler(options: { router: typeof edgeStoreRouter }) {
  return async (request: Request): Promise<Response> => {
    try {
      const url = new URL(request.url);
      const pathSegments = url.pathname.split('/').filter(Boolean);
      
      const edgeStorePath = pathSegments.slice(2).join('/');
      
      const edgeStoreUrl = new URL(`/${edgeStorePath}`, config.storage.edgeStore.baseUrl);
      edgeStoreUrl.search = url.search;
      
      const requestHeaders: Record<string, string> = {};
      request.headers.forEach((value, key) => {
        requestHeaders[key] = value;
      });
      
      const forwardedRequest = new Request(edgeStoreUrl.toString(), {
        method: request.method,
        headers: {
          ...requestHeaders,
          'Authorization': `Bearer ${config.storage.edgeStore.accessKey}`,
          'X-EdgeStore-Secret': config.storage.edgeStore.secretKey,
        },
        body: request.body,
      });
      
      const response = await fetch(forwardedRequest);
      
      const responseHeaders: Record<string, string> = {};
      response.headers.forEach((value, key) => {
        responseHeaders[key] = value;
      });
      
      return new Response(response.body, {
        status: response.status,
        statusText: response.statusText,
        headers: {
          ...responseHeaders,
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        },
      });
    } catch (error) {
      console.error('EdgeStore handler error:', error);
      return new Response(JSON.stringify({ error: 'Internal server error' }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }
  };
}