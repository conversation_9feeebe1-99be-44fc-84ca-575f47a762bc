import { cn } from "@workspace/ui/lib/utils";
import type { VariantProps } from "class-variance-authority";
import { cva } from "class-variance-authority";
import type React from "react";

export const badge = cva(
	["inline-block", "rounded-none", "px-3", "py-1", "text-xs", "leading-tight"],
	{
		variants: {
			status: {
				success: ["bg-emerald-500/10", "text-emerald-500", "border border-emerald-500/50"],
				info: ["bg-primary/10", "text-primary"],
				warning: ["bg-amber-500/10", "text-amber-500", "border border-amber-500/50"],
				error: ["bg-rose-500/10", "text-rose-500"],
				feature: ["bg-sky-500/10", "text-sky-500"],
				improvement: ["bg-amber-500/10", "text-amber-500"],
				fix: ["bg-rose-500/10", "text-rose-500"],
				release: ["bg-emerald-500/10", "text-emerald-500"],
			},
			variant: {
				primary: "bg-primary/10 text-primary",
			},
		},
		defaultVariants: {
			status: "info",
		},
	},
);

export type BadgeProps = React.HtmlHTMLAttributes<HTMLDivElement> &
	VariantProps<typeof badge>;

export const Badge = ({
	children,
	className,
	status,
	variant,
	...props
}: BadgeProps) => (
	<span className={cn(badge({ status, variant }), className)} {...props}>
		{children}
	</span>
);

Badge.displayName = "Badge";