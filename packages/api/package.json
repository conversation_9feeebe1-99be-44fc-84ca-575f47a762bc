{"dependencies": {"@hono/zod-openapi": "^1.0.2", "@scalar/hono-api-reference": "^0.8.2", "@sindresorhus/slugify": "^2.2.1", "@workspace/ai": "workspace:*", "@workspace/config": "workspace:*", "@workspace/database": "workspace:*", "@workspace/mail": "workspace:*", "@workspace/storage": "workspace:*", "@workspace/utils": "workspace:*", "hono": "^4.7.8", "hono-openapi": "^0.4.8", "nanoid": "^5.1.5", "openai": "^4.96.2", "openapi-merge": "^1.3.3", "use-intl": "^4.1.0", "zod": "^3.24.3"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@types/react": "19.1.3", "@workspace/typescript-config": "workspace:*", "encoding": "^0.1.13", "prisma": "^6.7.0", "typescript": "5.8.3"}, "main": "./index.ts", "name": "@workspace/api", "scripts": {"type-check": "tsc --noEmit"}, "version": "0.0.0"}