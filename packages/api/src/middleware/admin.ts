import { type Session, auth } from "@workspace/database/convex/auth";
import { createMiddleware } from "hono/factory";
import { convertToConvexSession } from "@workspace/database/convex/auth/utils";

export const adminMiddleware = createMiddleware<{
	Variables: {
		session: Session["session"];
		user: Session["user"];
	};
}>(async (c, next) => {
	const authSession = await auth.api.getSession({
		headers: c.req.raw.headers,
	});

	if (!authSession) {
		return c.json({ error: "Unauthorized" }, 401);
	}

	const session = convertToConvexSession(authSession);

	// Check if user has admin role
	if (!session.user.role || session.user.role !== "admin") {
		return c.json({ error: "Forbidden" }, 403);
	}

	c.set("session", session.session);
	c.set("user", session.user);

	await next();
});
