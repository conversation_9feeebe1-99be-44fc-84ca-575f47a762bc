import { streamText, textModel } from "@workspace/ai";
import {
	AiChatSchema,
	api,
	convex,
} from "@workspace/database";
import { Hono } from "hono";
import { describeRoute } from "hono-openapi";
import { resolver, validator } from "hono-openapi/zod";
import { HTTPException } from "hono/http-exception";
import { z } from "zod";
import { authMiddleware } from "../middleware/auth";
import { verifyOrganizationMembership } from "./organizations/lib/membership";
import { AiChatRole } from "@workspace/database/convex/helpers/enums";
import { Id } from "@workspace/database/convex/_generated/dataModel";

export const aiRouter = new Hono()
	.basePath("/ai")
	.use(authMiddleware)
	.get(
		"/chats",
		describeRoute({
			tags: ["AI"],
			summary: "Get chats",
			description: "Get all chats for current user or organization",
			responses: {
				200: {
					description: "Chats",
					content: {
						"application/json": {
							schema: resolver(z.object({ chats: z.array(AiChatSchema as unknown as z.<PERSON>od<PERSON>ypeAny) })),
						},
					},
				},
			},
		}),
		validator(
			"query",
			z.object({ organizationId: z.string().optional() }).optional(),
		),
		async (c) => {
			const query = c.req.valid("query");
			const chats = await (query?.organizationId
				? convex.query(api.aiChats.getAiChatsByOrganizationId, {
						limit: 10,
						offset: 0,
						organizationId: query?.organizationId as Id<"organizations">,
					})
				: convex.query(api.aiChats.getAiChatsByUserId, {
						limit: 10,
						offset: 0,
						userId: c.get("user")._id as Id<"users">,
					}));

			return c.json({ chats });
		},
	)
	.get(
		"/chats/:id",
		describeRoute({
			tags: ["AI"],
			summary: "Get chat",
			description: "Get a chat by id",
			responses: {
				200: {
					description: "Chat",
					content: {
						"application/json": {
							schema: resolver(z.object({ chat: AiChatSchema as unknown as z.ZodTypeAny })),
						},
					},
				},
			},
		}),
		async (c) => {
			const { id } = c.req.param();

			const chat = await convex.query(api.aiChats.getAiChatById, {
				id: id as Id<"aiChats">,
			});

			if (!chat) {
				throw new HTTPException(404, { message: "Chat not found" });
			}

			if (chat.organizationId) {
				await verifyOrganizationMembership(
					chat.organizationId,
					c.get("user")._id,
				);
			} else if (chat.userId !== c.get("user")._id) {
				throw new HTTPException(403, { message: "Forbidden" });
			}

			return c.json({ chat });
		},
	)
	.post(
		"/chats",
		describeRoute({
			tags: ["AI"],
			summary: "Create chat",
			description: "Create a new chat",
			responses: {
				200: {
					description: "Chat",
					content: {
						"application/json": {
							schema: resolver(z.object({ chat: AiChatSchema as unknown as z.ZodTypeAny })),
						},
					},
				},
			},
		}),
		validator(
			"json",
			z.object({
				title: z.string().optional(),
				organizationId: z.string().optional(),
			}),
		),
		async (c) => {
			const { title, organizationId } = c.req.valid("json");
			const user = c.get("user");

			if (organizationId) {
				await verifyOrganizationMembership(organizationId, user._id);
			}

			const chat = await convex.mutation(api.aiChats.createAiChat, {
				title,
				organizationId: organizationId as Id<"organizations">,
				userId: user._id as Id<"users">,
			});

			if (!chat) {
				throw new HTTPException(500, {
					message: "Failed to create chat",
				});
			}

			return c.json({ chat });
		},
	)
	.put(
		"/chats/:id",
		describeRoute({
			tags: ["AI"],
			summary: "Update chat",
			description: "Update a chat by id",
			responses: {
				200: {
					description: "Chat",
					content: {
						"application/json": {
							schema: resolver(z.object({ chat: AiChatSchema as unknown as z.ZodTypeAny })),
						},
					},
				},
			},
		}),
		validator("json", z.object({ title: z.string().optional() })),
		async (c) => {
			const { id } = c.req.param();
			const { title } = c.req.valid("json");
			const user = c.get("user");

			const chat = await convex.query(api.aiChats.getAiChatById, {
				id: id as Id<"aiChats">,
			});

			if (!chat) {
				throw new HTTPException(404, { message: "Chat not found" });
			}

			if (chat.organizationId) {
				await verifyOrganizationMembership(
					chat.organizationId,
					user._id,
				);
			} else if (chat.userId !== c.get("user")._id) {
				throw new HTTPException(403, { message: "Forbidden" });
			}

			const updatedChat = await convex.mutation(api.aiChats.updateAiChat, {
				id: id as Id<"aiChats">,
				title,
			});

			return c.json({ chat: updatedChat });
		},
	)
	.delete(
		"/chats/:id",
		describeRoute({
			tags: ["AI"],
			summary: "Delete chat",
			description: "Delete a chat by id",
			responses: {
				204: {
					description: "Chat deleted",
				},
			},
		}),
		async (c) => {
			const { id } = c.req.param();
			const user = c.get("user");
			const chat = await convex.query(api.aiChats.getAiChatById, {
				id: id as Id<"aiChats">,
			});

			if (!chat) {
				throw new HTTPException(404, { message: "Chat not found" });
			}

			if (chat.organizationId) {
				await verifyOrganizationMembership(
					chat.organizationId,
					user._id,
				);
			} else if (chat.userId !== c.get("user")._id) {
				throw new HTTPException(403, { message: "Forbidden" });
			}

			await convex.mutation(api.aiChats.deleteAiChat, {
				id: id as Id<"aiChats">,
			});

			return c.body(null, 204);
		},
	)
	.post(
		"/chats/:id/messages",
		describeRoute({
			tags: ["AI"],
			summary: "Add message to chat",
			description:
				"Send all messages of the chat to the AI model to get a response",
			responses: {
				200: {
					description:
						"Returns a stream of the response from the AI model",
				},
			},
		}),
		validator(
			"json",
			z.object({
				messages: z.array(
					z.object({
						role: z.enum([AiChatRole.USER, AiChatRole.ASSISTANT]),
						content: z.string(),
					}),
				),
			}),
		),
		async (c) => {
			const { id } = c.req.param();
			const { messages } = c.req.valid("json");
			const user = c.get("user");

			const chat = await convex.query(api.aiChats.getAiChatById, {
				id: id as Id<"aiChats">,
			});

			if (!chat) {
				throw new HTTPException(404, { message: "Chat not found" });
			}

			if (chat.organizationId) {
				await verifyOrganizationMembership(
					chat.organizationId,
					user._id,
				);
			} else if (chat.userId !== c.get("user")._id) {
				throw new HTTPException(403, { message: "Forbidden" });
			}

			const response = streamText({
				model: textModel,
				messages,
				async onFinish({ text }) {
					await convex.mutation(api.aiChats.updateAiChat, {
						id: id as Id<"aiChats">,
						messages: [
							...messages,
							{
								role: AiChatRole.ASSISTANT,
								content: text,
							},
						],
					});
				},
			});

			return response.toDataStreamResponse({
				sendUsage: true,
			});
		},
	);