import { sendEmail } from "@workspace/mail";
import { Hono } from "hono";
import { describeRoute } from "hono-openapi";
import { resolver, validator } from "hono-openapi/zod";
import { z } from "zod";

export const newsletterRouter = new Hono().basePath("/newsletter").post(
	"/signup",
	validator(
		"form",
		z.object({
			email: z.string().email(),
		}),
	),
	describeRoute({
		tags: ["Newsletter"],
		summary: "Sign up for the newsletter",
		description: "Takes an email and sends a confirmation email",
		responses: {
			204: {
				description: "Email sent",
			},
			500: {
				description: "Could not send email",
				content: {
					"application/json": {
						schema: resolver(z.object({ error: z.string() })),
					},
				},
			},
		},
	}),
	async (c) => {
		const { email } = c.req.valid("form");

		try {
			await sendEmail({
				to: email,
				templateId: "newsletterSignup",
				context: undefined,
			});

			return c.body(null, 204);
		} catch (error) {
			console.error(error);
			return c.json({ error: "Could not send email" }, 500);
		}
	},
);
