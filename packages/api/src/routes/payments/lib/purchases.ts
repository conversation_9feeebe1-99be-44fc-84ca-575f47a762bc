import { api } from "@workspace/database/convex/_generated/api";
import { Id } from "@workspace/database/convex/_generated/dataModel";
import { convex } from "@workspace/database/convex/helpers/client";

export const getPurchases = async (
	props: { organizationId: string } | { userId: string },
) => {
	if ("organizationId" in props) {
		const { organizationId } = props;
		const purchases = await convex.query(api.purchases.getPurchasesByOrganizationId, {
			organizationId: organizationId as Id<"organizations">,
		});

		return purchases;
	}

	const { userId } = props;

	const purchases = await convex.query(api.purchases.getPurchasesByUserId, {
		userId: userId as Id<"users">,
	});

	return purchases;
};
