import { type Config, config } from "@workspace/config";
import { api, convex } from "@workspace/database";
import { Hono } from "hono";
import { describeRoute } from "hono-openapi";
import { resolver, validator } from "hono-openapi/zod";
import { z } from "zod";
import { authMiddleware } from "../../middleware/auth";
import { verifyOrganizationMembership } from "../organizations/lib/membership";
import { PurchaseSchema } from "@workspace/database/convex/helpers/zod";
import { HTTPException } from "hono/http-exception";
import { Id } from "@workspace/database/convex/_generated/dataModel";
import { getBaseUrl } from "@workspace/utils";

const plans = config.payments.plans as Config["payments"]["plans"];

export const paymentsRouter = new Hono()
	.basePath("/payments")
	.use(authMiddleware)
	.get(
		"/purchases",
		describeRoute({
			tags: ["Payments"],
			summary: "Get purchases",
			description: "Get all purchases for current user or organization",
			responses: {
				200: {
					description: "Purchases",
					content: {
						"application/json": {
							schema: resolver(z.object({ purchases: z.array(PurchaseSchema as unknown as z.ZodTypeAny) })),
						},
					},
				},
			},
		}),
		validator(
			"query",
			z.object({ organizationId: z.string().optional() }).optional(),
		),
		async (c) => {
			const query = c.req.valid("query");
			const user = c.get("user");

			const organizationId = query?.organizationId;

			if (organizationId) {
				await verifyOrganizationMembership(organizationId, user._id);
				const purchases = await convex.query(api.purchases.getPurchasesByOrganizationId, {
					organizationId: organizationId as Id<"organizations">,
				});
				return c.json({ purchases });
			}

			const purchases = await convex.query(api.purchases.getPurchasesByUserId, {
				userId: user._id as Id<"users">,
			});
			return c.json({ purchases });
		},
	)
	.post(
		"/create-checkout-link",
		authMiddleware,
		validator(
			"query",
			z.object({
				type: z.enum(["one-time", "subscription"]),
				productId: z.string(),
				redirectUrl: z.string().optional(),
				organizationId: z.string().optional(),
			}),
		),
		describeRoute({
			tags: ["Payments"],
			summary: "Create a checkout link",
			description:
				"Creates a checkout link for a one-time or subscription product",
			responses: {
				200: {
					description: "Checkout link",
				},
			},
		}),
		async (c) => {
			const { productId, redirectUrl, type, organizationId } =
				c.req.valid("query");
			const user = c.get("user");

			const customerId = await convex.query(api.purchases.getCustomerIdFromEntity,
				organizationId
					? { organizationId: organizationId as Id<"organizations"> }
					: { userId: user._id as Id<"users"> }
			);

			const plan = Object.entries(plans).find(([_planId, plan]) =>
				plan.prices?.find((price) => price.productId === productId),
			);
			const price = plan?.[1].prices?.find(
				(price) => price.productId === productId,
			);
			const trialPeriodDays =
				price && "trialPeriodDays" in price
					? price.trialPeriodDays
					: undefined;

			const organization = organizationId
				? await convex.query(api.organizations.getOrganizationById, { id: organizationId as Id<"organizations"> })
				: undefined;

			if (organization === null) {
				throw new HTTPException(404);
			}

			const seats =
				organization && price && "seatBased" in price && price.seatBased
					? organization.members.length
					: undefined;

			try {
				const checkoutLink = await convex.action(api.polar.generateCheckoutLink, {
					productIds: [productId],
					origin: getBaseUrl(),
					successUrl: redirectUrl || `${getBaseUrl()}/success`,
				});

				if (!checkoutLink) {
					throw new HTTPException(500);
				}

				return c.json({ checkoutLink });
			} catch (e) {
				console.error(e);
				throw new HTTPException(500);
			}
		},
	)
	.post(
		"/create-customer-portal-link",
		authMiddleware,
		validator(
			"query",
			z.object({
				purchaseId: z.string(),
				redirectUrl: z.string().optional(),
			}),
		),
		describeRoute({
			tags: ["Payments"],
			summary: "Create a customer portal link",
			description:
				"Creates a customer portal link for the customer or team. If a purchase is provided, the link will be created for the customer of the purchase.",
			responses: {
				200: {
					description: "Customer portal link",
				},
			},
		}),
		async (c) => {
			const { purchaseId, redirectUrl } = c.req.valid("query");
			const user = c.get("user");

			const purchase = await convex.query(api.purchases.getPurchaseById, { id: purchaseId as any });

			if (!purchase) {
				throw new HTTPException(403);
			}

			if (purchase.organizationId) {
				const userOrganizationMembership =
					await convex.query(api.organizations.getOrganizationMembership, {
						userId: user._id as any,
						organizationId: purchase.organizationId,
					});
				if (userOrganizationMembership?.role !== "owner") {
					throw new HTTPException(403);
				}
			}

			if (purchase.userId && purchase.userId !== user._id) {
				throw new HTTPException(403);
			}

			try {
				const customerPortalLink = await convex.action(api.polar.generateCustomerPortalUrl, {});
				if (!customerPortalLink) {
					throw new HTTPException(500);
				}

				return c.json({ customerPortalLink });
			} catch (e) {
				console.error("Could not create customer portal link", e);
				throw new HTTPException(500);
			}
		},
	);

