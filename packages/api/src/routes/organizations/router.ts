import { api } from "@workspace/database/convex/_generated/api";
import { convex } from "@workspace/database/convex/helpers/client";
import slugify from "@sindresorhus/slugify";
import { Hono } from "hono";
import { describeRoute } from "hono-openapi";
import { validator } from "hono-openapi/zod";
import { nanoid } from "nanoid";
import { z } from "zod";
import { ERROR_MESSAGES } from "@workspace/database/convex/helpers/errors";

export const organizationsRouter = new Hono().basePath("/organizations").get(
	"/generate-slug",
	validator(
		"query",
		z.object({
			name: z.string(),
		}),
	),
	describeRoute({
		summary: "Generate a slug for an organization",
		tags: ["Organizations"],
	}),
	async (c) => {
		const { name } = c.req.valid("query");

		const baseSlug = slugify(name, {
			lowercase: true,
		});

		let slug = baseSlug;
		let hasAvailableSlug = false;

		for (let i = 0; i < 3; i++) {
			const existing = await convex.query(api.organizations.getOrganizationBySlug, {
				slug,
			});

			if (!existing) {
				hasAvailableSlug = true;
				break;
			}

			slug = `${baseSlug}-${nanoid(5)}`;
		}

		if (!hasAvailableSlug) {
			return c.json(
				{
					error: ERROR_MESSAGES.NO_AVAILABLE_SLUG,
				},
				400,
			);
		}

		return c.json({
			slug,
		});
	},
);
