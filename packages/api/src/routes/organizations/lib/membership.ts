import { api } from "@workspace/database/convex/_generated/api";
import { Id } from "@workspace/database/convex/_generated/dataModel";
import { convex } from "@workspace/database/convex/helpers/client";
import { ERROR_MESSAGES } from "@workspace/database/convex/helpers/errors";
import { HTTPException } from "hono/http-exception";

export async function verifyOrganizationMembership(
	organizationId: string,
	userId: string,
) {
	const membership = await convex.query(api.organizations.getOrganizationMembership, {
		organizationId: organizationId as Id<"organizations">,
		userId: userId as Id<"users">,
	});

	if (!membership) {
		throw new HTTPException(404, {
			message: ERROR_MESSAGES.ORGANIZATION_MEMBERSHIP,
		});
	}

	return {
		organization: membership.organization,
		role: membership.role,
	};
}
