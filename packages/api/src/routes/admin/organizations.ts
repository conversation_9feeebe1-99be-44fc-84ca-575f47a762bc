import { Hono } from "hono";
import { api } from "@workspace/database/convex/_generated/api";
import { convex } from "@workspace/database/convex/helpers/client";
import { describeRoute } from "hono-openapi";
import { validator } from "hono-openapi/zod";
import { HTTPException } from "hono/http-exception";
import { z } from "zod";
import { adminMiddleware } from "../../middleware/admin";
import { Id } from "@workspace/database/convex/_generated/dataModel";

export const organizationRouter = new Hono()
	.basePath("/organizations")
	.use(adminMiddleware)
	.get(
		"/",
		validator(
			"query",
			z.object({
				query: z.string().optional(),
				limit: z.string().optional().default("10").transform(Number),
				offset: z.string().optional().default("0").transform(Number),
			}),
		),
		describeRoute({
			summary: "Get all organizations",
			tags: ["Administration"],
		}),
		async (c) => {
			const { query, limit, offset } = c.req.valid("query");

			const organizations = await convex.query(api.organizations.getOrganizations, {
				limit,
				offset,
				search: query,
			});

			const total = await convex.query(api.organizations.countAllOrganizations, {
				limit,
				offset,
				query,
			});

			return c.json({ organizations, total });
		},
	)
	.get("/:id", async (c) => {
		const id = c.req.param("id");

		const organization = await convex.query(api.organizations.getOrganizationById, {
			id: id as Id<"organizations">,
		});

		if (!organization) {
			throw new HTTPException(404);
		}

		return c.json(organization);
	});
