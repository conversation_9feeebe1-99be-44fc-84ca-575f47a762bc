import { Link, Text } from "@react-email/components";
import PrimaryButton from "../src/components/PrimaryButton";
import Wrapper from "../src/components/Wrapper";

export function NewUser({ url, name, otp }: { url: string; name: string; otp: string }) {
	return (
		<Wrapper>
			<Text>Hello {name},</Text>

			<Text>
				Your one-time password is
				<br />
				<strong className="font-bold text-2xl">{otp}</strong>
			</Text>

			<Text>Click the link below to confirm your email address:</Text>

			<PrimaryButton href={url}>
				Confirm email address &rarr;
			</PrimaryButton>

			<Text className="text-muted-foreground text-sm">
				Or open this link in your browser: <Link href={url}>{url}</Link>
			</Text>
		</Wrapper>
	);
}

NewUser.PreviewProps = {
	url: "#",
	name: "<PERSON>",
	otp: "123456",
};

export default NewUser;
