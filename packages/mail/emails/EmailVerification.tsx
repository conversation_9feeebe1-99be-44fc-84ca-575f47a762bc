import { Link, Text } from "@react-email/components";
import React from "react";
import PrimaryButton from "../src/components/PrimaryButton";
import Wrapper from "../src/components/Wrapper";

export function EmailVerification({ url, name }: { url: string; name: string }) {
	return (
		<Wrapper>
			<Text>Hello {name},</Text>

			<PrimaryButton href={url}>
				Confirm email address &rarr;
			</PrimaryButton>

			<Text className="text-muted-foreground text-sm">
				Or open this link in your browser: <Link href={url}>{url}</Link>
			</Text>
		</Wrapper>
	);
}

EmailVerification.PreviewProps = {
	url: "#",
	name: "<PERSON>",
};

export default EmailVerification;
