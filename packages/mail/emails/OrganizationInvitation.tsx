import { Heading, Link, Text } from "@react-email/components";
import PrimaryButton from "../src/components/PrimaryButton";
import Wrapper from "../src/components/Wrapper";

export function OrganizationInvitation({
	url,
	organizationName,
}: {
	url: string;
	organizationName: string;
}) {
	return (
		<Wrapper>
			<Heading className="text-xl">
				Invitation to join {organizationName}
			</Heading>
			<Text>
				You have been invited to join {organizationName}
			</Text>

			<PrimaryButton href={url}>
				Join
			</PrimaryButton>

			<Text className="mt-4 text-muted-foreground text-sm">
				Or open this link in your browser: <Link href={url}>{url}</Link>
			</Text>
		</Wrapper>
	);
}

OrganizationInvitation.PreviewProps = {
	url: "#",
	organizationName: "Acme",
};

export default OrganizationInvitation;
