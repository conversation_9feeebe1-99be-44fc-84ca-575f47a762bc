import { Link, Text } from "@react-email/components";
import PrimaryButton from "../src/components/PrimaryButton";
import Wrapper from "../src/components/Wrapper";

export function MagicLink({ url }: { url: string }) {
	return (
		<Wrapper>
			<Text>
				Click the link below to log in to your account:
			</Text>

			<Text>Use this link</Text>

			<PrimaryButton href={url}>
				Log in &rarr;
			</PrimaryButton>

			<Text className="text-muted-foreground text-sm">
				Or open this link in your browser: <Link href={url}>{url}</Link>
			</Text>
		</Wrapper>
	);
}

MagicLink.PreviewProps = {
	url: "#",
};

export default MagicLink;
