import { Link, Text } from "@react-email/components";
import React from "react";
import PrimaryButton from "../src/components/PrimaryButton";
import Wrapper from "../src/components/Wrapper";

export function ForgotPassword({ url, name }: { url: string; name: string }) {
	return (
		<Wrapper>
			<Text>Hello {name},</Text>

			<PrimaryButton href={url}>
				Reset password &rarr;
			</PrimaryButton>

			<Text className="text-muted-foreground text-sm">
				Or open this link in your browser: <Link href={url}>{url}</Link>
			</Text>
		</Wrapper>
	);
}

ForgotPassword.PreviewProps = {
	url: "#",
	name: "<PERSON>",
};

export default ForgotPassword;
