import type { Send<PERSON><PERSON><PERSON><PERSON><PERSON> } from "../../types";

// ConvexProvider for sending emails through Convex Resend
export class ConvexProvider {
  private convexClient: any;

  constructor(convexClient?: any) {
    this.convexClient = convexClient;
  }

  setConvexClient(client: any) {
    this.convexClient = client;
  }

  // Send email using Convex Resend component
  send: SendEmailHandler = async ({ to, subject, html, text }) => {
    if (!this.convexClient) {
      throw new Error("Convex client not configured. Use setConvexClient() to configure it.");
    }

    try {
      // Use the Convex client to call the sendEmail mutation
      await this.convexClient.mutation("resend:sendEmail", {
        to,
        subject,
        html,
        text,
      });
    } catch (error) {
      console.error("Failed to send email via Convex:", error);
      throw new Error("Could not send email via Convex");
    }
  };

  // Send template email using Convex
  async sendTemplate<T extends string>(params: {
    to: string;
    templateId: T;
    context: any;
  }) {
    if (!this.convexClient) {
      throw new Error("Convex client not configured. Use setConvexClient() to configure it.");
    }

    try {
      await this.convexClient.action("resend:sendTemplateEmail", {
        to: params.to,
        templateId: params.templateId,
        context: params.context,
      });
    } catch (error) {
      console.error("Failed to send template email via Convex:", error);
      throw new Error("Could not send template email via Convex");
    }
  }
}

// Export a default instance
export const convexProvider = new ConvexProvider();

// Export the send function for backward compatibility
export const send: SendEmailHandler = convexProvider.send; 