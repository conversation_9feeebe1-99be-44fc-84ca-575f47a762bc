import { config } from "@workspace/config";
import type { mailTemplates } from "../../emails";
import { send } from "../provider";
import type { TemplateId } from "./templates";
import { getTemplate } from "./templates";

export async function sendEmail<T extends TemplateId>(
	params: {
		to: string;
	} & (
		| {
				templateId: T;
				context: Parameters<(typeof mailTemplates)[T]>[0];
		  }
		| {
				subject: string;
				text?: string;
				html?: string;
		  }
	),
) {
	const { to } = params;

	let html: string;
	let text: string;
	let subject: string;

	if ("templateId" in params) {
		const { templateId, context } = params;
		const template = await getTemplate({
			templateId,
			context,
		});
		subject = template.subject as string;
		text = template.text;
		html = template.html;
	} else {
		subject = params.subject;
		text = params.text ?? "";
		html = params.html ?? "";
	}

	try {
		await send({
			to,
			subject,
			text,
			html,
		});
		return true;
	} catch (e) {
		return false;
	}
}
