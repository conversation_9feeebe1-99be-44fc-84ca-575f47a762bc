import { render } from "@react-email/render";
import { mailTemplates } from "../../emails";

export type TemplateId = keyof typeof mailTemplates;

// Render a template for Convex
export async function renderTemplateForConvex<T extends TemplateId>(
  templateId: T,
  context: Parameters<(typeof mailTemplates)[T]>[0]
) {
  const template = mailTemplates[templateId];
  
  if (!template) {
    throw new Error(`Template ${templateId} not found`);
  }

  const email = template({
    ...(context as any),
  });

  const subject =
    "subject" in email && typeof email.subject === "string"
      ? email.subject
      : `Email from StartWithConvex`;

  const html = await render(email);
  const text = await render(email, { plainText: true });
  
  return { html, text, subject };
}

// Send a template email using Convex
export async function sendConvexTemplateEmail<T extends TemplateId>(params: {
  to: string;
  templateId: T;
  context: Parameters<(typeof mailTemplates)[T]>[0];
  convexClient: any;
}) {
  const { to, templateId, context, convexClient } = params;
  
  try {
    // Render the template
    const { html, text, subject } = await renderTemplateForConvex(templateId, context);
    
    // Send via Convex
    await convexClient.mutation("resend:sendEmail", {
      to,
      subject,
      html,
      text,
    });
    
    return { success: true };
  } catch (error) {
    console.error("Failed to send Convex template email:", error);
    return { success: false, error };
  }
}

export type TemplateContexts = {
  [K in TemplateId]: Parameters<(typeof mailTemplates)[K]>[0];
}; 