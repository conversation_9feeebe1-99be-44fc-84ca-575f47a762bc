import { render } from "@react-email/render";
import { mailTemplates } from "../../emails";

export async function getTemplate<T extends TemplateId>({
	templateId,
	context,
}: {
	templateId: T;
	context: Parameters<(typeof mailTemplates)[T]>[0];
}) {
	const template = mailTemplates[templateId];

	const email = template({
		...(context as any),
	});

	const subject =
		"subject" in email
			? email.subject
			: "";

	const html = await render(email);
	const text = await render(email, { plainText: true });
	return { html, text, subject };
}

export type TemplateId = keyof typeof mailTemplates;
