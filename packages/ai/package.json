{"dependencies": {"@ai-sdk/anthropic": "^1.2.10", "@ai-sdk/openai": "^1.3.21", "@ai-sdk/react": "^1.2.11", "ai": "^4.3.13", "openai": "^4.96.2"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@workspace/typescript-config": "workspace:*", "@types/react": "19.1.3", "typescript": "5.8.3"}, "main": "./index.ts", "name": "@workspace/ai", "scripts": {"type-check": "tsc --noEmit"}, "types": "./**/.tsx", "version": "0.0.0"}