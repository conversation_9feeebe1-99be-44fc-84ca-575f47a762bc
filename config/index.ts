import type { Config } from "./types";

export const config = {
	appName: "StartWithConvex Demo",
	// Organizations
	organizations: {
		// Whether organizations are enabled in general
		enable: true,
		// Whether billing for organizations should be enabled (below you can enable it for users instead)
		enableBilling: true,
		// Whether the organization should be hidden from the user (use this for multi-tenant applications)
		hideOrganization: false,
		// Should users be able to create new organizations? Otherwise only admin users can create them
		enableUsersToCreateOrganizations: true,
		// Whether users should be required to be in an organization. This will redirect users to the organization page after sign in
		requireOrganization: true,
		// Define forbidden organization slugs. Make sure to add all paths that you define as a route after /app/... to avoid routing issues
		forbiddenOrganizationSlugs: [
			"new-organization",
			"admin",
			"settings",
			"ai-chat",
			"organization-invitation",
		],
	},
	// Users
	users: {
		// Whether billing should be enabled for users (above you can enable it for organizations instead)
		enableBilling: false,
		// Whether you want the user to go through an onboarding form after signup (can be defined in the OnboardingForm.tsx)
		enableOnboarding: true,
	},
	// Authentication
	auth: {
		// Whether users should be able to create accounts (otherwise users can only be by admins)
		enableSignup: true,
		// Whether users should be able to sign in with a magic link
		enableMagicLink: true,
		// Whether users should be able to sign in with a social provider
		enableSocialLogin: true,
		// Whether users should be able to sign in with a passkey
		enablePasskeys: true,
		// Whether users should be able to sign in with a password
		enablePasswordLogin: true,
		// Whether users should be activate two factor authentication
		enableTwoFactor: true,
		// where users should be redirected after the sign in
		redirectAfterSignIn: "/app",
		// where users should be redirected after logout
		redirectAfterLogout: "/",
		// how long a session should be valid
		sessionCookieMaxAge: 60 * 60 * 24 * 30,
	},
	// Mails
	mails: {
		// the from address for mails
		from: "<EMAIL>",
	},
	// Frontend
	ui: {
		// the themes that should be available in the app
		enabledThemes: ["light", "dark"],
		// the default theme
		defaultTheme: "light",
		// the saas part of the application
		saas: {
			// whether the saas part should be enabled (otherwise all routes will be redirect to the marketing page)
			enabled: true,
			// whether the sidebar layout should be used
			useSidebarLayout: true,
		},
		// the marketing part of the application
		marketing: {
			// whether the marketing features should be enabled (otherwise all routes will be redirect to the saas part)
			enabled: true,
		},
	},
	// Storage
	storage: {
		// define the name of the buckets for the different types of files
		bucketNames: {
			avatars: process.env.NEXT_PUBLIC_AVATARS_BUCKET_NAME ?? "avatars",
		},
		edgeStore: {
			accessKey: process.env.EDGE_STORE_ACCESS_KEY ?? "",
			secretKey: process.env.EDGE_STORE_SECRET_KEY ?? "",
			baseUrl: process.env.EDGE_STORE_BASE_URL ?? "https://api.edgestore.dev",
		},
	},
	contactForm: {
		// whether the contact form should be enabled
		enabled: true,
		// the email to which the contact form messages should be sent
		to: "<EMAIL>",
		// the subject of the email
		subject: "Contact form message",
	},
	// Payments
	payments: {
		// define the products that should be available in the checkout
		plans: {
			free: {
				isFree: true,
				name: "Free Plan",
				description: "Free plan for testing purposes.",
				interval: "month",
				prices: [
					{
						type: "free",
						productId: "",
						amount: 0,
						currency: "USD",
					},
				],
			},
			pro: {
				recommended: true,
				name: "Premium Monthly",
				description: "All premium features for one low monthly price.",
				prices: [
					{
						type: "recurring",
						productId: process.env.NEXT_PUBLIC_POLAR_PRO_MONTHLY as string,
						interval: "month",
						amount: 10,
						currency: "USD",
						seatBased: true,
						trialPeriodDays: 7,
					},
					{
						type: "recurring",
						productId: process.env.NEXT_PUBLIC_POLAR_PRO_YEARLY as string,
						interval: "year",
						amount: 100,
						currency: "USD",
						seatBased: true,
						trialPeriodDays: 7,
					},
				],
			},
			proPlus: {
				name: "Premium Plus Monthly",
				description: "Premium features plus advanced tools for monthly subscribers.",
				prices: [
					{
						type: "recurring",
						productId: process.env.NEXT_PUBLIC_POLAR_PRO_PLUS_MONTHLY as string,
						interval: "month",
						amount: 20,
						currency: "USD",
						seatBased: true,
						trialPeriodDays: 7,
					},
					{
						type: "recurring",
						productId: process.env.NEXT_PUBLIC_POLAR_PRO_PLUS_YEARLY as string,
						interval: "year",
						amount: 200,
						currency: "USD",
						seatBased: true,
						trialPeriodDays: 7,
					},
				],
			},
		},
	},
} as const satisfies Config;

export type { Config };
