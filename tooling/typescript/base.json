{"$schema": "https://json.schemastore.org/tsconfig", "compilerOptions": {"allowJs": true, "composite": false, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "incremental": true, "inlineSources": false, "isolatedModules": true, "module": "ESNext", "target": "ES2021", "moduleResolution": "bundler", "noUnusedLocals": false, "noUnusedParameters": false, "preserveWatchOutput": true, "resolveJsonModule": true, "skipLibCheck": true, "strict": true, "strictNullChecks": true, "types": ["node"]}, "display": "<PERSON><PERSON><PERSON>", "exclude": ["node_modules", "dist", ".next", "build"]}