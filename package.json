{"name": "shadcn-ui-monorepo", "version": "0.0.1", "private": true, "scripts": {"build": "turbo build", "dev": "dotenv -c -- turbo dev --concurrency 15", "lint": "turbo lint", "format": "prettier --write \"**/*.{ts,tsx,md}\""}, "dependencies": {"@convex-dev/better-auth": "0.7.7", "better-auth": "1.2.12", "dotenv": "^16.4.7", "zod": "^4.0.5"}, "devDependencies": {"@types/node": "^24.0.14", "@types/react": "^19.1.8", "@workspace/typescript-config": "workspace:*", "dotenv-cli": "^8.0.0", "prettier": "^3.5.1", "turbo": "^2.4.2", "typescript": "5.7.3"}, "packageManager": "pnpm@10.4.1", "engines": {"node": ">=20"}}