# Storage Package

This package provides file storage utilities with EdgeStore integration.

## Setup

1. Install dependencies (already done in this project):
```bash
pnpm add @edgestore/server @edgestore/react zod
```

2. Set up environment variables:
```bash
EDGE_STORE_ACCESS_KEY=your-access-key-here
EDGE_STORE_SECRET_KEY=your-secret-key-here
EDGE_STORE_BASE_URL=https://api.edgestore.dev
NEXT_PUBLIC_AVATARS_BUCKET_NAME=avatars
```

3. Get your EdgeStore keys from the [EdgeStore Dashboard](https://dashboard.edgestore.dev/)

## Usage

### Backend (API)

The storage package is already integrated into the API routes at `/uploads`. Use the signed upload URL endpoint:

```typescript
// POST /api/uploads/signed-upload-url
{
  "bucket": "avatars",
  "filename": "profile.jpg",
  "contentType": "image/jpeg"
}
```

### Frontend

Use the EdgeStore provider and hooks:

```typescript
import { EdgeStoreProvider, useEdgeStore } from '@workspace/storage';

// In your app layout
<EdgeStoreProvider>
  {children}
</EdgeStoreProvider>

// In your components
const { edgestore } = useEdgeStore();

const uploadFile = async (file: File) => {
  const res = await edgestore.avatars.upload({
    file,
    onProgressChange: (progress) => {
      console.log(progress);
    },
  });
  console.log(res.url);
};
```

## Available Buckets

- `avatars` - User profile images (4MB limit, JPEG/PNG only)
- `documents` - Documents and text files (50MB limit)
- `images` - General images (10MB limit)
- `videos` - Video files (500MB limit)
- `audio` - Audio files (100MB limit)
- `archives` - Compressed files (100MB limit)
- `other` - Other file types (25MB limit)

## File Categories

The package includes utilities to automatically categorize files based on MIME type or file extension:

```typescript
import { getFileCategoryFromMimeType, getFileTypeFromExtension } from '@workspace/storage';

const category = getFileCategoryFromMimeType('image/jpeg'); // 'images'
const type = getFileTypeFromExtension('pdf'); // 'documents'
``` 