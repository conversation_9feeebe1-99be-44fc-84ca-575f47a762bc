# Convex Resend Email Integration

This guide explains how to use the Convex Resend component with your organized mail templates.

## Overview

The integration provides multiple ways to send emails:

1. **Basic Convex Resend** - Simple email sending within Convex functions
2. **Template Emails** - Using built-in templates in Convex
3. **React Email Integration** - Using your existing React Email templates
4. **Mail Package Integration** - Using the organized mail package from your frontend

## Setup

### 1. Environment Variables

Add your Resend API key to your environment:

```bash
RESEND_API_KEY=your_resend_api_key_here
```

### 2. Convex Configuration

The Resend component is already configured in `convex.config.ts`:

```typescript
import { defineApp } from "convex/server";
import resend from "@convex-dev/resend/convex.config";

const app = defineApp();
app.use(resend);
export default app;
```

## Usage Examples

### 1. Basic Email Sending (Within Convex)

```typescript
import { api } from "./_generated/api";

// In a Convex mutation or action
export const sendWelcomeEmail = mutation({
  args: { userId: v.id("users") },
  handler: async (ctx, { userId }) => {
    const user = await ctx.db.get(userId);
    
    await ctx.runMutation(api.resend.sendEmail, {
      to: user.email,
      subject: "Welcome!",
      html: "<h1>Welcome to our app!</h1>",
    });
  },
});
```

### 2. Using Built-in Templates (Within Convex)

```typescript
// Send a magic link email
await ctx.runAction(api.resend.sendMagicLinkEmail, {
  to: "<EMAIL>",
  magicLink: "https://yourapp.com/login?token=...",
});

// Send organization invitation
await ctx.runAction(api.resend.sendOrganizationInvitation, {
  to: "<EMAIL>",
  organizationName: "Acme Corp",
  inviterName: "John Doe", 
  invitationLink: "https://yourapp.com/invite?token=...",
});
```

### 3. Using React Email Templates (From Frontend/Actions)

For more complex templates, use your existing React Email templates with the mail package:

```typescript
// In a Convex action
import { renderTemplateForConvex } from "@workspace/mail";

export const sendCustomEmail = action({
  args: { 
    to: v.string(),
    templateId: v.string(),
    context: v.any(),
  },
  handler: async (ctx, { to, templateId, context }) => {
    // Render your React Email template
    const { html, text, subject } = await renderTemplateForConvex(
      templateId as any,
      context
    );
    
    // Send using Convex Resend
    await ctx.runMutation(api.resend.sendCustomTemplateEmail, {
      to,
      subject,
      html,
      text,
    });
  },
});
```

### 4. Frontend Integration

Use the Convex provider from your frontend:

```typescript
import { convexProvider } from "@workspace/mail";
import { useConvex } from "convex/react";

function EmailComponent() {
  const convex = useConvex();
  
  // Configure the provider
  convexProvider.setConvexClient(convex);
  
  const sendEmail = async () => {
    // Send using template
    await convexProvider.sendTemplate({
      to: "<EMAIL>",
      templateId: "magicLink",
      context: { magicLink: "https://..." },
    });
  };
  
  return <button onClick={sendEmail}>Send Email</button>;
}
```

## Available Templates

### Built-in Templates (Convex)

- `magicLink` - Sign-in magic links
- `emailVerification` - Email verification links
- `organizationInvitation` - Organization invitations
- `forgotPassword` - Password reset links
- `newUser` - Welcome emails for new users
- `newsletterSignup` - Newsletter confirmations

### React Email Templates (Mail Package)

Your existing templates in `packages/mail/emails/`:

- `EmailVerification.tsx`
- `ForgotPassword.tsx`
- `MagicLink.tsx`
- `NewsletterSignup.tsx`
- `NewUser.tsx`
- `OrganizationInvitation.tsx`

## Best Practices

### 1. Use Actions for External Template Rendering

When using React Email templates, use Convex actions since they can import external packages:

```typescript
export const sendReactEmailTemplate = action({
  // ... action implementation
});
```

### 2. Error Handling

Always wrap email sending in try-catch blocks:

```typescript
try {
  await ctx.runMutation(api.resend.sendEmail, {
    to: user.email,
    subject: "Welcome!",
    html: emailHtml,
  });
} catch (error) {
  console.error("Failed to send email:", error);
  // Handle error appropriately
}
```

### 3. Template Context Types

Use TypeScript for better type safety:

```typescript
import type { TemplateContexts } from "@workspace/mail";

// Get proper types for template contexts
type MagicLinkContext = TemplateContexts["magicLink"];
```

### 4. Environment-Specific Configuration

Configure different sender addresses for different environments:

```typescript
const from = process.env.NODE_ENV === "production" 
  ? "<EMAIL>"
  : "<EMAIL>";
```

## Migration Guide

If you're migrating from the basic HTTP Resend provider:

1. Update imports to use Convex functions
2. Replace direct API calls with Convex mutations/actions
3. Use the `convexProvider` for frontend email sending
4. Gradually migrate templates to use the new system

## Troubleshooting

### Common Issues

1. **"Convex client not configured"** - Make sure to call `convexProvider.setConvexClient(convex)` before using
2. **Import errors** - Remember that mutations can't import external packages, use actions instead
3. **Template not found** - Check that the template ID matches exactly

### Webhooks

If you need to handle Resend webhooks (delivery status, bounces, etc.), you can add webhook handling to the HTTP router in `http.ts`. The current setup focuses on sending emails rather than webhook handling.

## Examples Repository

Check the `packages/mail` folder for complete examples of:
- React Email templates
- Provider configurations  
- Utility functions
- Type definitions 