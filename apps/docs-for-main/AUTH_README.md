# Convex Better Auth Integration

This package provides a clean integration between Better Auth and Convex, following best practices from the official documentation.

## Setup

### 1. Environment Variables

Add these environment variables to your Convex deployment:

```bash
# Social auth providers
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
GITHUB_CLIENT_ID=your_github_client_id
GITHUB_CLIENT_SECRET=your_github_client_secret
```

### 2. Convex Configuration

The authentication is already configured in:
- `packages/database/convex/convex.config.ts` - Better Auth component
- `packages/database/convex/auth.config.ts` - Server config
- `packages/database/convex/auth.ts` - Auth functions
- `packages/database/convex/http.ts` - HTTP routes

### 3. Client Usage

```tsx
import { authClient } from "@workspace/database/convex/auth";

// Sign in with email/password
const { data, error } = await authClient.signIn.email({
  email: "<EMAIL>",
  password: "password123"
});

// Sign in with Google
const { data, error } = await authClient.signIn.social({
  provider: "google"
});

// Get current session
const session = authClient.useSession();
```

### 4. Server Usage

```ts
import { getCurrentUser } from "@workspace/database/convex/auth";

export const myQuery = query({
  args: {},
  handler: async (ctx) => {
    const user = await getCurrentUser(ctx);
    if (!user) {
      throw new Error("Not authenticated");
    }
    
    // User is authenticated
    return { message: `Hello ${user.name}!` };
  },
});
```

## Features

- ✅ Email/password authentication
- ✅ Google OAuth
- ✅ GitHub OAuth  
- ✅ Automatic user sync with Convex database
- ✅ Session management
- ✅ Simplified configuration

## Architecture

This setup uses:
- **Server**: Convex with Better Auth component integration
- **Client**: Better Auth React client
- **Database**: Convex (no Prisma needed)
- **Auth Functions**: Managed by Better Auth component

The auth system is now fully integrated with Convex and follows the official patterns from the documentation. 