{"name": "@workspace/web", "version": "0.0.1", "type": "module", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "typecheck": "tsc --noEmit", "generate-docs": "fumadocs generate", "content": "content-collections"}, "dependencies": {"@convex-nudge/react": "^1.1.1", "@fumadocs/content-collections": "^1.2.1", "@hono/zod-validator": "^0.7.2", "@hookform/resolvers": "^5.1.1", "@shikijs/rehype": "^3.8.1", "@tabler/icons-react": "^3.34.0", "@tanstack/react-query": "^5.83.0", "@vemetric/web": "^0.5.4", "@vercel/analytics": "^1.5.0", "@workspace/api": "workspace:*", "@workspace/config": "workspace:*", "@workspace/database": "workspace:*", "@workspace/ui": "workspace:*", "@workspace/utils": "workspace:*", "convex": "^1.25.4", "date-fns": "^4.1.0", "dotenv": "^17.2.0", "embla-carousel-autoplay": "^8.6.0", "fumadocs-core": "^15.6.4", "fumadocs-mdx": "^11.6.11", "fumadocs-ui": "^15.6.4", "geist": "^1.4.2", "hono": "^4.7.8", "jotai": "^2.12.5", "js-cookie": "^3.0.5", "lucide-react": "^0.475.0", "next": "^15.2.3", "next-themes": "^0.4.4", "nprogress": "^0.2.0", "nuqs": "^2.4.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.60.0", "slugify": "^1.6.6", "sonner": "^2.0.6", "ufo": "^1.6.1", "zod-openapi": "^5.2.0"}, "devDependencies": {"@content-collections/core": "^0.8.2", "@content-collections/mdx": "^0.2.2", "@content-collections/next": "^0.2.6", "@mdx-js/mdx": "^3.1.0", "@types/js-cookie": "^3.0.4", "@types/mdx": "^2.0.13", "@types/node": "^20", "@types/nprogress": "^0.2.3", "@types/react": "^19", "@types/react-dom": "^19", "@workspace/typescript-config": "workspace:*", "mdx": "^0.3.1", "typescript": "^5.7.3"}}