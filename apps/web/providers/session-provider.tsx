"use client";

import { sessionQ<PERSON><PERSON><PERSON><PERSON>, useSessionQuery } from "@/modules/auth/lib/api";
import { authClient } from "@workspace/database/convex/auth/client";
import { useQueryClient } from "@tanstack/react-query";
import { type ReactNode, useEffect, useState } from "react";
import { SessionContext } from "@/lib/session-context";
import { Session } from "@workspace/database/convex/auth";
import { convertToConvexSession } from "@workspace/database/convex/auth/utils";

export function SessionProvider({ children }: { children: ReactNode }) {
	const queryClient = useQueryClient();

	const { data: session } = useSessionQuery();
	const [loaded, setLoaded] = useState(!!session);

	useEffect(() => {
		if (session && !loaded) {
			setLoaded(true);
		}
	}, [session]);

	// Convert Better Auth session to Convex session
	const convexSession = session ? convertToConvexSession(session) : null;

	return (
		<SessionContext.Provider
			value={{
				loaded,
				session: convexSession?.session ?? null,
				user: convexSession?.user as Session["user"] | null,
				reloadSession: async () => {
					const { data: newSession, error } =
						await authClient.getSession({
							query: {
								disableCookieCache: true,
							},
						});

					if (error) {
						throw new Error(
							error.message || "Failed to fetch session",
						);
					}

					queryClient.setQueryData(sessionQueryKey, () => newSession);
				},
			}}
		>
			{children}
		</SessionContext.Provider>
	);
}
