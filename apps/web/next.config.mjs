import { config } from 'dotenv';
import { resolve } from 'path';
import { withContentCollections } from "@content-collections/next";

// environment variables from the root .env.local file
config({ path: resolve(process.cwd(), '../../.env.local') });

/** @type {import('next').NextConfig} */
const nextConfig = {
  transpilePackages: ["@workspace/api", "@workspace/ui", "@workspace/database"],
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "firebasestorage.googleapis.com",
      },
    ],
  },
}

export default withContentCollections(nextConfig);
