"var Component=(()=>{var u=Object.create;var s=Object.defineProperty;var x=Object.getOwnPropertyDescriptor;var _=Object.getOwnPropertyNames;var l=Object.getPrototypeOf,j=Object.prototype.hasOwnProperty;var p=(n,e)=>()=>(e||n((e={exports:{}}).exports,e),e.exports),f=(n,e)=>{for(var o in e)s(n,o,{get:e[o],enumerable:!0})},c=(n,e,o,a)=>{if(e&&typeof e==\"object\"||typeof e==\"function\")for(let r of _(e))!j.call(n,r)&&r!==o&&s(n,r,{get:()=>e[r],enumerable:!(a=x(e,r))||a.enumerable});return n};var g=(n,e,o)=>(o=n!=null?u(l(n)):{},c(e||!n||!n.__esModule?s(o,\"default\",{value:n,enumerable:!0}):o,n)),M=n=>c(s({},\"__esModule\",{value:!0}),n);var i=p((C,h)=>{h.exports=_jsx_runtime});var w={};f(w,{default:()=>m});var t=g(i());function d(n){let e={h2:\"h2\",p:\"p\",...n.components};return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(e.h2,{children:\"This is a heading\"}),`\n`,(0,t.jsx)(e.p,{children:\"And we also have some great content here. What do you think?\"})]})}function m(n={}){let{wrapper:e}=n.components||{};return e?(0,t.jsx)(e,{...n,children:(0,t.jsx)(d,{...n})}):d(n)}return M(w);})();\n;return Component;"