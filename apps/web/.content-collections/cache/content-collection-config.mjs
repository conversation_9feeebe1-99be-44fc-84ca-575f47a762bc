// content-collections.ts
import { defineCollection, defineConfig } from "@content-collections/core";
import { compileMDX } from "@content-collections/mdx";
import rehypeShiki from "@shikijs/rehype";
import { remarkImage } from "fumadocs-core/mdx-plugins";
function sanitizePath(path) {
  return path.replace(/(\.[a-zA-Z-]{2,5})$/, "").replace(/^\//, "").replace(/\/$/, "").replace(/index$/, "");
}
var posts = defineCollection({
  name: "posts",
  directory: "content/posts",
  include: "**/*.{mdx,md}",
  schema: (z) => ({
    title: z.string(),
    date: z.string(),
    image: z.string().optional(),
    authorName: z.string(),
    authorImage: z.string().optional(),
    authorLink: z.string().optional(),
    excerpt: z.string().optional(),
    tags: z.array(z.string()),
    published: z.boolean()
  }),
  transform: async (document, context) => {
    const body = await compileMDX(context, document, {
      rehypePlugins: [
        [
          rehypeShiki,
          {
            theme: "nord"
          }
        ]
      ]
    });
    return {
      ...document,
      body,
      path: sanitizePath(document._meta.path)
    };
  }
});
var legalPages = defineCollection({
  name: "legalPages",
  directory: "content/legal",
  include: "**/*.{mdx,md}",
  schema: (z) => ({
    title: z.string()
  }),
  transform: async (document, context) => {
    const body = await compileMDX(context, document);
    return {
      ...document,
      body,
      path: sanitizePath(document._meta.path)
    };
  }
});
var docs = defineCollection({
  name: "docs",
  directory: "content/docs",
  include: "**/*.mdx",
  schema: (z) => ({
    title: z.string(),
    description: z.string().optional(),
    icon: z.string().optional(),
    full: z.boolean().optional(),
    sidebar: z.object({
      label: z.string().optional(),
      order: z.number().optional()
    }).optional(),
    toc: z.array(z.object({
      title: z.string(),
      url: z.string(),
      depth: z.number()
    })).optional()
  }),
  transform: async (document, context) => {
    try {
      const body = await compileMDX(context, document, {
        remarkPlugins: [
          [
            remarkImage,
            {
              publicDir: "public"
            }
          ]
        ]
      });
      return {
        ...document,
        body
      };
    } catch (error) {
      console.warn(`Warning: Failed to compile MDX for ${document._meta.path}:`, error?.message || error);
      return {
        ...document,
        body: { code: "", frontmatter: {} }
      };
    }
  }
});
var docsMeta = defineCollection({
  name: "docsMeta",
  directory: "content/docs",
  include: "**/meta.json",
  parser: "json",
  schema: (z) => ({
    title: z.string().optional(),
    label: z.string().optional(),
    description: z.string().optional(),
    pages: z.array(z.string()).optional(),
    icon: z.string().optional(),
    order: z.number().optional(),
    defaultOpen: z.boolean().optional()
  })
});
var content_collections_default = defineConfig({
  collections: [posts, legalPages, docs, docsMeta]
});
export {
  content_collections_default as default
};
