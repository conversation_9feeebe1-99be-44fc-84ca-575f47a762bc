"var Component=(()=>{var u=Object.create;var r=Object.defineProperty;var h=Object.getOwnPropertyDescriptor;var x=Object.getOwnPropertyNames;var m=Object.getPrototypeOf,y=Object.prototype.hasOwnProperty;var f=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),_=(e,t)=>{for(var n in t)r(e,n,{get:t[n],enumerable:!0})},s=(e,t,n,i)=>{if(t&&typeof t==\"object\"||typeof t==\"function\")for(let c of x(t))!y.call(e,c)&&c!==n&&r(e,c,{get:()=>t[c],enumerable:!(i=h(t,c))||i.enumerable});return e};var j=(e,t,n)=>(n=e!=null?u(m(e)):{},s(t||!e||!e.__esModule?r(n,\"default\",{value:e,enumerable:!0}):n,e)),M=e=>s(r({},\"__esModule\",{value:!0}),e);var a=f((w,d)=>{d.exports=_jsx_runtime});var g={};_(g,{default:()=>p});var o=j(a());function l(e){let t={code:\"code\",p:\"p\",...e.components};return(0,o.jsxs)(t.p,{children:[\"This is the placeholder page for your privacy policy. Edit the \",(0,o.jsx)(t.code,{children:\"content/legal/privacy-policy.md\"}),\" file to add your own content here.\"]})}function p(e={}){let{wrapper:t}=e.components||{};return t?(0,o.jsx)(t,{...e,children:(0,o.jsx)(l,{...e})}):l(e)}return M(g);})();\n;return Component;"