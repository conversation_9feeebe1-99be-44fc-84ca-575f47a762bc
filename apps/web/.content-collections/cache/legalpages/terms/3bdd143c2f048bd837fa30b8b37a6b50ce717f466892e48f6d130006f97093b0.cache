"var Component=(()=>{var u=Object.create;var c=Object.defineProperty;var h=Object.getOwnPropertyDescriptor;var x=Object.getOwnPropertyNames;var p=Object.getPrototypeOf,f=Object.prototype.hasOwnProperty;var _=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),j=(e,t)=>{for(var n in t)c(e,n,{get:t[n],enumerable:!0})},d=(e,t,n,s)=>{if(t&&typeof t==\"object\"||typeof t==\"function\")for(let r of x(t))!f.call(e,r)&&r!==n&&c(e,r,{get:()=>t[r],enumerable:!(s=h(t,r))||s.enumerable});return e};var y=(e,t,n)=>(n=e!=null?u(p(e)):{},d(t||!e||!e.__esModule?c(n,\"default\",{value:e,enumerable:!0}):n,e)),M=e=>d(c({},\"__esModule\",{value:!0}),e);var a=_((C,i)=>{i.exports=_jsx_runtime});var g={};j(g,{default:()=>m});var o=y(a());function l(e){let t={code:\"code\",p:\"p\",...e.components};return(0,o.jsxs)(t.p,{children:[\"This is the placeholder page for your terms and conditions. Edit the \",(0,o.jsx)(t.code,{children:\"content/legal/terms.md\"}),\" file to add your own content here.\"]})}function m(e={}){let{wrapper:t}=e.components||{};return t?(0,o.jsx)(t,{...e,children:(0,o.jsx)(l,{...e})}):l(e)}return M(g);})();\n;return Component;"