"var Component=(()=>{var x=Object.create;var r=Object.defineProperty;var d=Object.getOwnPropertyDescriptor;var f=Object.getOwnPropertyNames;var h=Object.getPrototypeOf,_=Object.prototype.hasOwnProperty;var g=(t,e)=>()=>(e||t((e={exports:{}}).exports,e),e.exports),j=(t,e)=>{for(var n in e)r(t,n,{get:e[n],enumerable:!0})},s=(t,e,n,i)=>{if(e&&typeof e==\"object\"||typeof e==\"function\")for(let o of f(e))!_.call(t,o)&&o!==n&&r(t,o,{get:()=>e[o],enumerable:!(i=d(e,o))||i.enumerable});return t};var l=(t,e,n)=>(n=t!=null?x(h(t)):{},s(e||!t||!t.__esModule?r(n,\"default\",{value:t,enumerable:!0}):n,t)),M=t=>s(r({},\"__esModule\",{value:!0}),t);var u=g((C,a)=>{a.exports=_jsx_runtime});var v={};j(v,{default:()=>p});var c=l(u());function m(t){let e={p:\"p\",...t.components};return(0,c.jsx)(e.p,{children:\"This is the overview page of the getting started section.\"})}function p(t={}){let{wrapper:e}=t.components||{};return e?(0,c.jsx)(e,{...t,children:(0,c.jsx)(m,{...t})}):m(t)}return M(v);})();\n;return Component;"