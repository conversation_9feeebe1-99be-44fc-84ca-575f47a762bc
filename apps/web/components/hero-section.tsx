import Link from 'next/link'
import { But<PERSON> } from '@workspace/ui/components/button'
import { ArrowRight } from 'lucide-react'
import Image from 'next/image'
import { AnimatedGroup } from '@workspace/ui/components/motion-primitives/animated-group'
import { Badge } from '@workspace/ui/components/badge'
import { changelog } from '../modules/changelog/data'

const transitionVariants = {
    item: {
        hidden: {
            opacity: 0,
            filter: 'blur(12px)',
            y: 12,
        },
        visible: {
            opacity: 1,
            filter: 'blur(0px)',
            y: 0,
            transition: {
                type: 'spring',
                bounce: 0.3,
                duration: 1.5,
            },
        },
    },
}

export default function HeroSection() {
    const latestChangelog = changelog[0]
    const isNew = new Date(latestChangelog.date).getTime() > Date.now() - 7 * 24 * 60 * 60 * 1000

    return (
        <div className="max-h-screen overflow-hidden">

            <main className="relative w-full overflow-hidden">
                <div aria-hidden className="z-2 absolute inset-0 isolate hidden opacity-50 contain-strict lg:block">
                    <div className="w-140 h-320 -translate-y-87.5 absolute left-0 top-0 -rotate-45 rounded-none bg-[radial-gradient(68.54%_68.72%_at_55.02%_31.46%,hsla(0,0%,85%,.08)_0,hsla(0,0%,55%,.02)_50%,hsla(0,0%,45%,0)_80%)]" />
                    <div className="h-320 absolute left-0 top-0 w-60 -rotate-45 rounded-none bg-[radial-gradient(50%_50%_at_50%_50%,hsla(0,0%,85%,.06)_0,hsla(0,0%,45%,.02)_80%,transparent_100%)] [translate:5%_-50%]" />
                    <div className="h-320 -translate-y-87.5 absolute left-0 top-0 w-60 -rotate-45 bg-[radial-gradient(50%_50%_at_50%_50%,hsla(0,0%,85%,.04)_0,hsla(0,0%,45%,.02)_80%,transparent_100%)]" />
                </div>
                

                <section className="overflow-hidden bg-white dark:bg-transparent">
                    <div className="relative mx-auto max-w-5xl px-6 py-28 lg:py-24">
                        <div className="relative z-10 mx-auto max-w-2xl text-center">
                            <AnimatedGroup variants={transitionVariants as any} className="mb-8">
                                <Link
                                    href="/changelog"
                                    className="cursor-pointer hover:bg-background dark:hover:border-t-border bg-muted group mx-auto flex w-fit items-center gap-4 rounded-none border p-1 pl-4 shadow-md shadow-zinc-950/5 transition-colors duration-300 dark:border-t-white/5 dark:shadow-zinc-950">
                                    {isNew && <Badge status="warning">New</Badge>}
                                    <span className="text-foreground text-sm max-w-64 truncate">{latestChangelog.title}</span>
                                    <span className="dark:border-background block h-4 w-0.5 border-l bg-white dark:bg-zinc-700"></span>
                                    <div className="bg-background group-hover:bg-muted size-6 overflow-hidden rounded-none duration-500">
                                        <div className="flex w-12 -translate-x-1/2 duration-500 ease-in-out group-hover:translate-x-0">
                                            <span className="flex size-6">
                                                <ArrowRight className="m-auto size-3" />
                                            </span>
                                            <span className="flex size-6">
                                                <ArrowRight className="m-auto size-3" />
                                            </span>
                                        </div>
                                    </div>
                                </Link>
                            </AnimatedGroup>

                            <h1 className="text-balance text-4xl font-semibold md:text-5xl lg:text-6xl">Launch your SaaS faster with real-time power</h1>
                            <p className="mx-auto my-8 max-w-2xl text-xl">Ship production-ready applications in days, not months. Built with Next.js and Convex, our template delivers enterprise-grade real-time features, authentication, and payments out of the box.</p>

                                <div className="mx-auto max-w-5xl px-6">
                                    <div className="mx-auto flex max-w-4xl items-center justify-center gap-x-12 gap-y-8 sm:gap-x-16 sm:gap-y-12">
                                        <a href="https://convex.dev?utm_source=startwithconvex" target="_blank"><Image className="grayscale h-8 w-auto dark:invert" src="/convex.png" alt="Convex Logo" width={160} height={32} /></a>
                                        <a href="https://better-auth.com/auth?utm_source=startwithconvex" target="_blank"><Image className="grayscale h-8 w-auto" src="/better-auth-logo.png" alt="Better Auth Logo" width={160} height={20} /></a>
                                        <a href="https://polar.sh?utm_source=startwithconvex" target="_blank"><Image className="h-4 w-auto brightness-0 invert" src="/polar-logo.png" alt="Polar Logo" width={160} height={20} /></a>
                                        <a href="https://resend.com?utm_source=startwithconvex" target="_blank"><Image className="grayscale h-4 w-auto" src="/resend.png" alt="Resend Logo" width={160} height={20} /></a>
                                        <a href="https://vercel.com?utm_source=startwithconvex" target="_blank"><Image className="h-4 w-auto brightness-0 invert" src="/vercel-logo.png" alt="Vercel Logo" width={160} height={20} /></a>
                                    </div>
                                </div>

                            <AnimatedGroup
                                variants={{
                                    container: {
                                        visible: {
                                            transition: {
                                                staggerChildren: 0.05,
                                                delayChildren: 0.75,
                                            },
                                        },
                                    },
                                    ...transitionVariants,
                                } as any}
                                className="mt-12 flex flex-col items-center justify-center gap-2 md:flex-row">
                                <div
                                    key={1}
                                    className="bg-foreground/10 rounded-none border p-0.5">
                                    <Button
                                        asChild
                                        size="lg"
                                        className="rounded-none px-5 text-base">
                                        <Link href="/auth/login">
                                            <span className="text-nowrap">Get Started</span>
                                        </Link>
                                    </Button>
                                </div>
                            </AnimatedGroup>
                        </div>
                    </div>

                    <div className="mx-auto -mt-16 max-w-7xl">
                        <div className="perspective-distant -mr-16 pl-16 lg:-mr-56 lg:pl-56">
                            <div className="[transform:rotateX(20deg);]">
                                <div className="lg:h-176 relative skew-x-[.36rad]">
                                    <div aria-hidden className="bg-linear-to-b from-background to-background z-1 absolute -inset-16 via-transparent sm:-inset-32" />
                                    <div aria-hidden className="bg-linear-to-r from-background to-background z-1 absolute -inset-16 bg-white/50 via-transparent sm:-inset-32 dark:bg-transparent" />

                                    <div aria-hidden className="absolute -inset-16 bg-[linear-gradient(to_right,var(--color-border)_1px,transparent_1px),linear-gradient(to_bottom,var(--color-border)_1px,transparent_1px)] bg-[size:24px_24px] [--color-border:var(--color-zinc-400)] sm:-inset-32 dark:[--color-border:color-mix(in_oklab,var(--color-white)_20%,transparent)]" />
                                    <div aria-hidden className="from-background z-11 absolute inset-0 bg-gradient-to-l" />
                                    <div aria-hidden className="z-2 absolute inset-0 size-full items-center px-5 py-24 [background:radial-gradient(125%_125%_at_50%_10%,transparent_40%,var(--color-background)_100%)]" />
                                    <div aria-hidden className="z-2 absolute inset-0 size-full items-center px-5 py-24 [background:radial-gradient(125%_125%_at_50%_10%,transparent_40%,var(--color-background)_100%)]" />

                                    <Image className="rounded-none z-1 relative border dark:hidden" src="https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/phx%2Fphx.png?alt=media&token=fccc2a0a-a337-496e-bd81-264362235f74" alt="tailus ui hero section" width={2880} height={2074} />
                                    <Image className="rounded-none) z-1 relative hidden border dark:block" src="https://firebasestorage.googleapis.com/v0/b/relio-217bd.appspot.com/o/phx%2Fphx.png?alt=media&token=fccc2a0a-a337-496e-bd81-264362235f74" alt="tailus ui hero section" width={2880} height={2074} />
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </main>
        </div>
    )
}
