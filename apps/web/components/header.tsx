"use client";

import { useState } from "react"
import { Menu, X } from "lucide-react"
import Link from "next/link"
import Image from "next/image"
import { Button } from "@workspace/ui/components/button"
import { IconBrandGithubFilled } from '@tabler/icons-react'

const menuItems = [
    { name: 'Blog', href: '/blog' },
    { name: 'Changelog', href: '/changelog' },
]

const Header = () => {
  const [menuState, setMenuState] = useState(false)
  return (
    <header>
    <nav data-state={menuState ? 'active' : 'inactive'} className="fixed z-20 w-full border-b border-dashed bg-white backdrop-blur md:relative dark:bg-zinc-950/50 lg:dark:bg-transparent">
        <div className="m-auto max-w-5xl px-6">
            <div className="flex items-center justify-between gap-6 py-3 lg:gap-0 lg:py-4">
                <div className="flex w-full justify-between lg:w-auto">
                    <Link href="/" aria-label="home" className="flex items-center space-x-2">
                    <Image className="rounded-md z-10 relative border dark:hidden" src="/phx-logo-light.png" alt="phalanx crm logo" width={32} height={32} />
                    <Image className="rounded-md z-10 relative hidden border dark:block" src="/phx-logo-dark.png" alt="phalanx crm logo" width={32} height={32} />
                    </Link>

                    <button onClick={() => setMenuState(!menuState)} aria-label={menuState ? 'Close Menu' : 'Open Menu'} className="relative z-20 -m-2.5 -mr-4 block cursor-pointer p-2.5 lg:hidden">
                        <Menu className="data-[state=active]:rotate-180 data-[state=active]:scale-0 data-[state=active]:opacity-0 m-auto size-6 duration-200" />
                        <X className="data-[state=active]:rotate-0 data-[state=active]:scale-100 data-[state=active]:opacity-100 absolute inset-0 m-auto size-6 -rotate-180 scale-0 opacity-0 duration-200" />
                    </button>
                </div>

                <div className="bg-background data-[state=active]:block lg:data-[state=active]:flex mb-6 hidden w-full flex-wrap items-center justify-end space-y-8 rounded-none border p-6 shadow-2xl shadow-zinc-300/20 md:flex-nowrap lg:m-0 lg:flex lg:w-fit lg:gap-6 lg:space-y-0 lg:border-transparent lg:bg-transparent lg:p-0 lg:shadow-none dark:shadow-none dark:lg:bg-transparent">
                    <div className="lg:pr-4">
                        <ul className="space-y-6 text-base lg:flex lg:gap-8 lg:space-y-0 lg:text-sm">
                            {menuItems.map((item, index) => (
                                <li key={index}>
                                    <Link href={item.href} className="text-muted-foreground hover:text-accent-foreground block duration-150">
                                        <span>{item.name}</span>
                                    </Link>
                                </li>
                            ))}
                        </ul>
                    </div>

                    <div className="lg:pr-4">
                        <ul className="text-base lg:flex lg:gap-8 lg:space-y-0 lg:text-sm">
                            <Link href="https://github.com/robertalv/startwithconvex" className="text-muted-foreground hover:text-accent-foreground block duration-150">
                                <IconBrandGithubFilled className="size-4 text-blue-400" />
                            </Link>
                        </ul>
                    </div>
                    

                    <div className="flex flex-col space-y-3 sm:flex-row sm:gap-3 sm:space-y-0 md:w-fit lg:border-l lg:pl-6">
                        <Button asChild variant="outline" size="sm" className="rounded-none">
                            <Link href="/auth/login">
                                <span>Login</span>
                            </Link>
                        </Button>
                    </div>
                </div>
            </div>
        </div>
    </nav>
    </header>
  )
}

export default Header;