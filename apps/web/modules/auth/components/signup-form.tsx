"use client";

import { zod<PERSON><PERSON>ol<PERSON> } from "@hookform/resolvers/zod";
import { config } from "@workspace/config";
import { useAuthErrorMessages } from "@/modules/shared/hooks/errors-messages";
import { OrganizationInvitationAlert } from "@/modules/organization/components/organization-invitation-alert";
import { Alert, AlertDescription, AlertTitle } from "@workspace/ui/components/alert";
import { Button } from "@workspace/ui/components/button";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@workspace/ui/components/form";
import { Input } from "@workspace/ui/components/input";
import {
	AlertTriangleIcon,
	ArrowRightIcon,
	EyeIcon,
	EyeOffIcon,
	MailboxIcon,
} from "lucide-react";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { useState } from "react";
import type { SubmitHandler } from "react-hook-form";
import { useForm } from "react-hook-form";
import { withQuer<PERSON> } from "ufo";
import {
	type OAuthProvider,
	oAuthProviders,
} from "@/modules/auth/components/oauth-providers";
import { SocialSigninButton } from "@/modules/auth/components/social-signin-button";
import { signupformSchema, type SignupFormValues } from "@/modules/auth/lib/validators";
import { useAuth } from "@/modules/auth/hooks/use-auth";

export function SignupForm({ prefillEmail }: { prefillEmail?: string }) {
	const router = useRouter();
	const searchParams = useSearchParams();

	const [showPassword, setShowPassword] = useState(false);
	const invitationId = searchParams.get("invitationId");
	const email = searchParams.get("email");
	const redirectTo = searchParams.get("redirectTo");

	const redirectPath = invitationId
		? `/organization-invitation/${invitationId}`
		: (redirectTo ?? config.auth.redirectAfterSignIn);

	const { signUpWithEmail, isLoading } = useAuth(redirectPath);

	const form = useForm<SignupFormValues>({
		resolver: zodResolver(signupformSchema),
		values: {
			name: "",
			email: prefillEmail ?? email ?? "",
			password: "",
		},
	});

	const invitationOnlyMode = !config.auth.enableSignup && invitationId;

	const onSubmit: SubmitHandler<SignupFormValues> = async (values) => {
		const error = await signUpWithEmail(values);
		if (error?.root) {
			form.setError("root", { message: error.root });
		}
	};

	return (
		<div>
			<h1 className="font-bold text-xl md:text-2xl">
				Create your account
			</h1>
			<p className="mt-1 mb-6 text-foreground/60">
				Sign up to get started with your new account
			</p>

			{form.formState.isSubmitSuccessful && !invitationOnlyMode ? (
				<Alert>
					<MailboxIcon />
					<AlertTitle>
						Verify your email
					</AlertTitle>
					<AlertDescription>
						Check your email for the verification link
					</AlertDescription>
				</Alert>
			) : (
				<>
					{invitationId && (
						<OrganizationInvitationAlert className="mb-6" />
					)}

					<Form {...form}>
						<form
							className="flex flex-col items-stretch gap-4"
							onSubmit={form.handleSubmit(onSubmit)}
						>
							{form.formState.isSubmitted &&
								form.formState.errors.root && (
									<Alert variant="destructive">
										<AlertTriangleIcon />
										<AlertDescription>
											{form.formState.errors.root.message}
										</AlertDescription>
									</Alert>
								)}

							<FormField
								control={form.control}
								name="name"
								render={({ field }) => (
									<FormItem>
										<FormLabel>
											Name
										</FormLabel>
										<FormControl>
											<Input {...field} placeholder="Your name" />
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="email"
								render={({ field }) => (
									<FormItem>
										<FormLabel>
											Email
										</FormLabel>
										<FormControl>
											<Input
												{...field}
												placeholder="<EMAIL>"
												autoComplete="email"
												readOnly={!!prefillEmail}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="password"
								render={({ field }) => (
									<FormItem>
										<FormLabel>
											Password
										</FormLabel>
										<FormControl>
											<div className="relative">
												<Input
													type={
														showPassword
															? "text"
															: "password"
													}
													className="pr-10"
													{...field}
													placeholder="Enter password"
													autoComplete="new-password"
												/>
												<button
													type="button"
													onClick={() =>
														setShowPassword(
															!showPassword,
														)
													}
													className="absolute inset-y-0 right-0 flex items-center pr-4 text-primary text-xl"
												>
													{showPassword ? (
														<EyeOffIcon className="size-4" />
													) : (
														<EyeIcon className="size-4" />
													)}
												</button>
											</div>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							<Button
								className="w-full"
								type="submit"
								variant="primary"
								disabled={isLoading}
							>
								Create account
							</Button>
						</form>
					</Form>

					{config.auth.enableSignup &&
						config.auth.enableSocialLogin && (
							<>
								<div className="relative my-6 h-4">
									<hr className="relative top-2" />
									<p className="-translate-x-1/2 absolute top-0 left-1/2 mx-auto inline-block h-4 bg-card px-2 text-center font-medium text-foreground/60 text-sm leading-tight">
										Continue with
									</p>
								</div>

								<div className="grid grid-cols-1 items-stretch gap-2 sm:grid-cols-2">
									{Object.keys(oAuthProviders).map(
										(providerId) => (
											<SocialSigninButton
												key={providerId}
												provider={
													providerId as OAuthProvider
												}
											/>
										),
									)}
								</div>
							</>
						)}
				</>
			)}

			<div className="mt-6 text-center text-sm">
				<span className="text-foreground/60">
					Already have an account?{" "}
				</span>
				<Link
					href={withQuery(
						"/auth/login",
						Object.fromEntries(searchParams.entries()),
					)}
					className="hover:underline"
				>
					Sign in
					<ArrowRightIcon className="ml-1 inline size-4 align-middle" />
				</Link>
			</div>
		</div>
	);
}
