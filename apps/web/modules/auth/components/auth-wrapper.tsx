import { Footer } from "./footer";
import { type PropsWithChildren } from "react";
import { cn } from "@workspace/ui/lib/utils";

export function AuthWrapper({
	children,
	contentClass,
}: PropsWithChildren<{ contentClass?: string }>) {
	return (
		<div className="flex min-h-[calc(100vh-100px)] w-full flex-col justify-center">
			<div className="flex flex-1 w-full flex-col items-center justify-center py-6">
				<div className="container flex justify-center">
					<main className={cn(contentClass)}>
						{children}
					</main>
				</div>
			</div>

			<div className="w-full flex justify-center">
				<Footer />
			</div>
		</div>
	);
}
