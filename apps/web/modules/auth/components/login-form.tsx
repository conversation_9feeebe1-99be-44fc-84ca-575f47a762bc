"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { config } from "@workspace/config";
import { OrganizationInvitationAlert } from "@/modules/organization/components/organization-invitation-alert";
import { Alert, AlertDescription, AlertTitle } from "@workspace/ui/components/alert";
import { But<PERSON> } from "@workspace/ui/components/button";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
} from "@workspace/ui/components/form";
import { Input } from "@workspace/ui/components/input";
import {
	AlertTriangleIcon,
	ArrowRightIcon,
	KeyIcon,
	MailboxIcon,
} from "lucide-react";
import Link from "next/link";
import { useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import type { SubmitHandler } from "react-hook-form";
import { useForm } from "react-hook-form";
import { withQuery } from "ufo";
import { type OAuthProvider, oAuthProviders } from "@/modules/auth/components/oauth-providers";
import { useSession } from "@/modules/shared/hooks/use-session";
import { LoginModeSwitch } from "./login-mode-switch";
import { SocialSigninButton } from "./social-signin-button";
import { loginformSchema, type LoginFormValues } from "@/modules/auth/lib/validators";
import { useAuth } from "@/modules/auth/hooks/use-auth";
import { useRouter } from "@/modules/shared/lib/router";
import { IconEye, IconEyeOff } from "@tabler/icons-react";

export function LoginForm() {
	const router = useRouter();
	const searchParams = useSearchParams();
	const { user, loaded: sessionLoaded } = useSession();
	const [showPassword, setShowPassword] = useState(false);

	const invitationId = searchParams.get("invitationId");
	const email = searchParams.get("email");
	const redirectTo = searchParams.get("redirectTo");

	const redirectPath = invitationId
		? `/organization-invitation/${invitationId}`
		: (redirectTo ?? config.auth.redirectAfterSignIn);

	const { signInWithEmail, signInWithPasskey, isLoading } = useAuth(redirectPath);

	const form = useForm<LoginFormValues>({
		resolver: zodResolver(loginformSchema),
		defaultValues: {
			email: email ?? "",
			password: "",
			mode: config.auth.enablePasswordLogin ? "password" : "magic-link",
		},
	});

	useEffect(() => {
		if (sessionLoaded && user) {
			router.replace(redirectPath);
		}
	}, [user, sessionLoaded]);

	const onSubmit: SubmitHandler<LoginFormValues> = async (values) => {
		const error = await signInWithEmail(values);
		if (error?.root) {
			form.setError("root", { message: error.root });
		}
	};

	const handlePasskeySignIn = async () => {
		const error = await signInWithPasskey();
		if (error?.root) {
			form.setError("root", { message: error.root });
		}
	};

	const signinMode = form.watch("mode");

	return (
		<div>
			<h1 className="font-bold text-xl md:text-2xl text-center mb-6">
				Login to your account
			</h1>

			{form.formState.isSubmitSuccessful &&
			signinMode === "magic-link" ? (
				<Alert variant="default">
					<MailboxIcon />
					<AlertTitle>
						Magic link sent
					</AlertTitle>
					<AlertDescription>
						Check your email for the magic link.
					</AlertDescription>
				</Alert>
			) : (
				<>
					{invitationId && (
						<OrganizationInvitationAlert className="mb-6" />
					)}

					<Form {...form}>
						<form
							className="space-y-4"
							onSubmit={form.handleSubmit(onSubmit)}
						>
							{config.auth.enableMagicLink &&
								config.auth.enablePasswordLogin && (
									<LoginModeSwitch
										activeMode={signinMode}
										onChange={(mode) =>
											form.setValue(
												"mode",
												mode as typeof signinMode,
											)
										}
									/>
								)}

							{form.formState.isSubmitted &&
								form.formState.errors.root?.message && (
									<Alert variant="destructive">
										<AlertTriangleIcon />
										<AlertTitle>
											{form.formState.errors.root.message}
										</AlertTitle>
									</Alert>
								)}

							<FormField
								control={form.control}
								name="email"
								render={({ field }) => (
									<FormItem>
										<FormLabel>
											Email
										</FormLabel>
										<FormControl>
											<Input
												placeholder="<EMAIL>"
												{...field}
												autoComplete="email"
											/>
										</FormControl>
									</FormItem>
								)}
							/>

							{config.auth.enablePasswordLogin &&
								signinMode === "password" && (
									<FormField
										control={form.control}
										name="password"
										render={({ field }) => (
											<FormItem>
												<div className="flex justify-between gap-4">
													<FormLabel>
														Password
													</FormLabel>

													<Link
														href="/auth/forgot-password"
														className="text-foreground/60 text-xs hover:underline"
													>
														Forgot password
													</Link>
												</div>
												<FormControl>
													<div className="relative">
														<Input
															placeholder="Enter password"
															type={
																showPassword
																	? "text"
																	: "password"
															}
															className="pr-10"
															{...field}
															autoComplete="current-password"
														/>
														<button
															type="button"
															onClick={() =>
																setShowPassword(
																	!showPassword,
																)
															}
															className="absolute inset-y-0 right-0 flex items-center pr-4 text-primary text-xl"
														>
															{showPassword ? (
																<IconEyeOff className="size-4" />
															) : (
																<IconEye className="size-4" />
															)}
														</button>
													</div>
												</FormControl>
											</FormItem>
										)}
									/>
								)}

							<Button
								className="w-full"
								type="submit"
								variant="primary"
								disabled={isLoading}
							>
								{signinMode === "magic-link"
									? "Send magic link"
									: "Login"}
							</Button>
						</form>
					</Form>

					{(config.auth.enablePasskeys ||
						(config.auth.enableSignup &&
							config.auth.enableSocialLogin)) && (
						<>
							<div className="relative my-6 h-4">
								<hr className="relative top-2" />
								<p className="-translate-x-1/2 absolute top-0 left-1/2 mx-auto inline-block h-4 bg-card px-2 text-center font-medium text-foreground/60 text-sm leading-tight">
									Continue with
								</p>
							</div>

							<div className="grid grid-cols-1 items-stretch gap-2 sm:grid-cols-2">
								{config.auth.enableSignup &&
									config.auth.enableSocialLogin &&
									Object.keys(oAuthProviders).map(
										(providerId) => (
											<SocialSigninButton
												key={providerId}
												provider={
													providerId as OAuthProvider
												}
											/>
										),
									)}

								{config.auth.enablePasskeys && (
									<Button
										variant="outline"
										className="w-full sm:col-span-2"
										onClick={handlePasskeySignIn}
										disabled={isLoading}
									>
										<KeyIcon className="mr-1.5 size-4 text-primary" />
										Login with passkey
									</Button>
								)}
							</div>
						</>
					)}

					{config.auth.enableSignup && (
						<div className="mt-6 text-center text-sm">
							<span className="text-foreground/60">
								Don&apos;t have an account?{" "}
							</span>
							<Link
								href={withQuery(
									"/auth/signup",
									Object.fromEntries(searchParams.entries()),
								)}
                className="hover:underline"
							>
								Create an account
								<ArrowRightIcon className="ml-1 inline size-4 align-middle" />
							</Link>
						</div>
					)}
				</>
			)}
		</div>
	);
}
