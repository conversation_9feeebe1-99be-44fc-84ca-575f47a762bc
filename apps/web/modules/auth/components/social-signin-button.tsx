"use client";

import { config } from "@workspace/config";
import { But<PERSON> } from "@workspace/ui/components/button";
import { useQueryState, parseAsString } from "nuqs";
import { oAuthProviders } from "./oauth-providers";
import { useAuth } from "@/modules/auth/hooks/use-auth";

export function SocialSigninButton({
	provider,
	className,
}: {
	provider: keyof typeof oAuthProviders;
	className?: string;
}) {
	const [invitationId] = useQueryState("invitationId", parseAsString);
	const providerData = oAuthProviders[provider];

	const redirectPath = invitationId
		? `/app/organization-invitation/${invitationId}`
		: config.auth.redirectAfterSignIn;

	const { signInWithOAuth, isLoading } = useAuth(redirectPath);

	const onSignin = () => {
		signInWithOAuth(provider);
	};

	return (
		<Button
			onClick={onSignin}
			variant="outline"
			type="button"
			className={className}
			disabled={isLoading}
		>
			{providerData.icon && (
				<i className="mr-2 text-primary">
					<providerData.icon className="size-4" />
				</i>
			)}
			{providerData.name}
		</Button>
	);
}
