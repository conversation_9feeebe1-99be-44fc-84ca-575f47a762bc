"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { Alert, AlertDescription, AlertTitle } from "@workspace/ui/components/alert";
import { Button } from "@workspace/ui/components/button";
import { AlertTriangleIcon, ArrowLeftIcon, MailboxIcon } from "lucide-react";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@workspace/ui/components/form";
import { Input } from "@workspace/ui/components/input";
import Link from "next/link";
import { useSearchParams } from "next/navigation";
import { useForm } from "react-hook-form";
import { resetPasswordSchema, type ResetPasswordFormValues } from "@/modules/auth/lib/validators";
import { useAuth } from "@/modules/auth/hooks/use-auth";
import { useState } from "react";
import { EyeIcon, EyeOffIcon } from "lucide-react";

export function ResetPasswordForm() {
	const searchParams = useSearchParams();
	const token = searchParams.get("token");
	const [showPassword, setShowPassword] = useState(false);

	const { resetPassword, isLoading } = useAuth();

	const form = useForm<ResetPasswordFormValues>({
		resolver: zodResolver(resetPasswordSchema),
		defaultValues: {
			token: token ?? "",
			password: "",
		},
	});

	const onSubmit = async (values: ResetPasswordFormValues) => {
		const error = await resetPassword(values);
		if (error?.root) {
			form.setError("root", { message: error.root });
		}
	};

	return (
		<>
			<h1 className="font-bold text-xl md:text-2xl">
				Reset your password
			</h1>
			<p className="mt-1 mb-6 text-foreground/60">
				Enter your new password below.
			</p>

			{form.formState.isSubmitSuccessful ? (
				<Alert>
					<MailboxIcon />
					<AlertTitle>
						Password reset successful
					</AlertTitle>
					<AlertDescription>
						You can now sign in with your new password.
					</AlertDescription>
				</Alert>
			) : (
				<Form {...form}>
					<form
						className="flex flex-col items-stretch gap-4"
						onSubmit={form.handleSubmit(onSubmit)}
					>
						{form.formState.isSubmitted && form.formState.errors.root && (
							<Alert variant="destructive">
								<AlertTriangleIcon />
								<AlertDescription>
									{form.formState.errors.root.message}
								</AlertDescription>
							</Alert>
						)}

						<FormField
							control={form.control}
							name="password"
							render={({ field }) => (
								<FormItem>
									<FormLabel>
										New password
									</FormLabel>
									<FormControl>
										<div className="relative">
											<Input
												type={showPassword ? "text" : "password"}
												className="pr-10"
												placeholder="Enter new password"
												autoComplete="new-password"
												{...field}
											/>
											<button
												type="button"
												onClick={() => setShowPassword(!showPassword)}
												className="absolute inset-y-0 right-0 flex items-center pr-4 text-primary text-xl"
											>
												{showPassword ? (
													<EyeOffIcon className="size-4" />
												) : (
													<EyeIcon className="size-4" />
												)}
											</button>
										</div>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						<Button
							className="w-full"
							type="submit"
							variant="primary"
							disabled={isLoading}
						>
							Reset password
						</Button>
					</form>
				</Form>
			)}

			<div className="mt-6 text-center text-sm">
				<Link href="/auth/login" className="hover:underline">
					<ArrowLeftIcon className="mr-1 inline size-4 align-middle" />
					Back to sign in
				</Link>
			</div>
		</>
	);
}
