"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { Alert, AlertDescription, AlertTitle } from "@workspace/ui/components/alert";
import { Button } from "@workspace/ui/components/button";
import { AlertTriangleIcon, ArrowLeftIcon } from "lucide-react";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@workspace/ui/components/form";
import {
	InputOTP,
	InputOTPGroup,
	InputOTPSeparator,
	InputOTPSlot,
} from "@workspace/ui/components/input-otp";
import Link from "next/link";
import { useSearchParams } from "next/navigation";
import { useForm } from "react-hook-form";
import { verifyFormSchema, type VerifyFormValues } from "@/modules/auth/lib/validators";
import { useAuth } from "@/modules/auth/hooks/use-auth";
import { config } from "@workspace/config";

export function VerifyForm() {
	const searchParams = useSearchParams();
	const invitationId = searchParams.get("invitationId");
	const redirectTo = searchParams.get("redirectTo");

	const redirectPath = invitationId
		? `/organization-invitation/${invitationId}`
		: (redirectTo ?? config.auth.redirectAfterSignIn);

	const { verifyTwoFactor, isLoading } = useAuth(redirectPath);

	const form = useForm<VerifyFormValues>({
		resolver: zodResolver(verifyFormSchema),
		defaultValues: {
			code: "",
		},
	});

	const onSubmit = async (values: VerifyFormValues) => {
		const error = await verifyTwoFactor(values);
		if (error?.root) {
			form.setError("root", { message: error.root });
		}
	};

	return (
		<>
			<h1 className="font-bold text-xl md:text-2xl">
				Two-factor authentication
			</h1>
			<p className="mt-1 mb-4 text-foreground/60">
				Enter the 6-digit code from your authenticator app to verify your identity.
			</p>

			<Form {...form}>
				<form
					className="flex flex-col items-stretch gap-4"
					onSubmit={form.handleSubmit(onSubmit)}
				>
					{form.formState.isSubmitted && form.formState.errors.root && (
						<Alert variant="destructive">
							<AlertTriangleIcon />
							<AlertDescription>
								{form.formState.errors.root.message}
							</AlertDescription>
						</Alert>
					)}

					<FormField
						control={form.control}
						name="code"
						render={({ field }) => (
							<FormItem className="flex flex-col items-center">
								<FormControl>
									<InputOTP
										maxLength={6}
										{...field}
										autoComplete="one-time-code"
										onChange={(value) => {
											field.onChange(value);
											if (value.length === 6) {
												form.handleSubmit(onSubmit)();
											}
										}}
									>
										<InputOTPGroup>
											<InputOTPSlot
												className="size-10 text-lg"
												index={0}
											/>
											<InputOTPSlot
												className="size-10 text-lg"
												index={1}
											/>
											<InputOTPSlot
												className="size-10 text-lg"
												index={2}
											/>
										</InputOTPGroup>
										<InputOTPSeparator className="opacity-40" />
										<InputOTPGroup>
											<InputOTPSlot
												className="size-10 text-lg"
												index={3}
											/>
											<InputOTPSlot
												className="size-10 text-lg"
												index={4}
											/>
											<InputOTPSlot
												className="size-10 text-lg"
												index={5}
											/>
										</InputOTPGroup>
									</InputOTP>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>

					<Button
						className="w-full"
						type="submit"
						variant="primary"
						disabled={isLoading}
					>
						Verify code
					</Button>
				</form>
			</Form>

			<div className="mt-6 text-center text-sm">
				<Link href="/auth/login" className="hover:underline">
					<ArrowLeftIcon className="mr-1 inline size-4 align-middle" />
					Back to sign in
				</Link>
			</div>
		</>
	);
}
