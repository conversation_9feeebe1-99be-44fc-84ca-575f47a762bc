import { But<PERSON> } from "@workspace/ui/components/button";
import { Badge } from "@workspace/ui/components/badge";

interface GoogleAuthButtonProps {
  onClick: () => void;
  isNewUser: boolean;
  isLoading: boolean;
  isLastUsed?: boolean;
}

export function GoogleAuthButton({ 
  onClick, 
  isNewUser, 
  isLoading,
  isLastUsed 
}: GoogleAuthButtonProps) {
  return (
    <div className="relative">
      <Button 
        variant="outline" 
        type="button" 
        className="w-full rounded-none" 
        onClick={onClick} 
        disabled={isLoading}
      >
        {isLoading ? (
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="h-4 w-4 animate-spin">
            <path strokeLinecap="round" strokeLinejoin="round" d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 13.803-3.7M4.031 9.865a8.25 8.25 0 0 1 13.803-3.7l3.181 3.182m0-4.991v4.99" />
          </svg>
        ) : (
          <>
            <svg className="mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
              <path
                d="M12.48 10.92v3.28h7.84c-.24 1.84-.853 3.187-1.787 4.133-1.147 1.147-2.933 2.4-6.053 2.4-4.827 0-8.6-3.893-8.6-8.72s3.773-8.72 8.6-8.72c2.6 0 4.507 1.027 5.907 2.347l2.307-2.307C18.747 1.44 16.133 0 12.48 0 5.867 0 .307 5.387.307 12s5.56 12 12.173 12c3.573 0 6.267-1.173 8.373-3.36 2.16-2.16 2.84-5.213 2.84-7.667 0-.76-.053-1.467-.173-2.053H12.48z"
                fill="currentColor"
              />
            </svg>
            {isNewUser ? "Register with Google" : "Continue with Google"}
          </>
        )}
      </Button>
      {isLastUsed && (
        <Badge className="absolute rounded-sm -right-2 -top-2">
          Last used
        </Badge>
      )}
    </div>
  );
}