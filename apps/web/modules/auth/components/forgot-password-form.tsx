"use client";

import { zod<PERSON><PERSON>ol<PERSON> } from "@hookform/resolvers/zod";
import { <PERSON>ert, AlertDescription, AlertTitle } from "@workspace/ui/components/alert";
import { Button } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import { AlertTriangleIcon, ArrowLeftIcon, MailboxIcon } from "lucide-react";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@workspace/ui/components/form";
import Link from "next/link";
import { useForm } from "react-hook-form";
import { forgotPasswordSchema, type ForgotPasswordFormValues } from "@/modules/auth/lib/validators";
import { useAuth } from "@/modules/auth/hooks/use-auth";

export function ForgotPasswordForm() {
	const { forgotPassword, isLoading } = useAuth();

	const form = useForm<ForgotPasswordFormValues>({
		resolver: zod<PERSON><PERSON><PERSON>ver(forgotPasswordSchema),
		defaultValues: {
			email: "",
		},
	});

	const onSubmit = async (values: ForgotPasswordFormValues) => {
		const error = await forgotPassword(values);
		if (error?.root) {
			form.setError("root", { message: error.root });
		}
	};

	return (
		<>
			<h1 className="font-bold text-xl md:text-2xl">
				Forgot your password?
			</h1>
			<p className="mt-1 mb-6 text-foreground/60">
				Enter your email address and we'll send you a link to reset your password.
			</p>

			{form.formState.isSubmitSuccessful ? (
				<Alert>
					<MailboxIcon />
					<AlertTitle>
						Check your email
					</AlertTitle>
					<AlertDescription>
						We've sent you a link to reset your password.
					</AlertDescription>
				</Alert>
			) : (
				<Form {...form}>
					<form
						className="flex flex-col items-stretch gap-4"
						onSubmit={form.handleSubmit(onSubmit)}
					>
						{form.formState.isSubmitted && form.formState.errors.root && (
							<Alert variant="destructive">
								<AlertTriangleIcon />
								<AlertDescription>
									{form.formState.errors.root.message}
								</AlertDescription>
							</Alert>
						)}

						<FormField
							control={form.control}
							name="email"
							render={({ field }) => (
								<FormItem>
									<FormLabel>
										Email
									</FormLabel>
									<FormControl>
										<Input
											{...field}
											placeholder="<EMAIL>"
											autoComplete="email"
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						<Button
							className="w-full"
							type="submit"
							variant="primary"
							disabled={isLoading}
						>
							Send reset link
						</Button>
					</form>
				</Form>
			)}

			<div className="mt-6 text-center text-sm">
				<Link href="/auth/login" className="hover:underline">
					<ArrowLeftIcon className="mr-1 inline size-4 align-middle" />
					Back to sign in
				</Link>
			</div>
		</>
	);
}
