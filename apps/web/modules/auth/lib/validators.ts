import { z } from "zod";

export const loginformSchema = z.union([
	z.object({
		mode: z.literal("magic-link"),
		email: z.string().email(),
	}),
	z.object({
		mode: z.literal("password"),
		email: z.string().email(),
		password: z.string().min(1),
	}),
]);
export type LoginFormValues = z.infer<typeof loginformSchema>;

export const signupformSchema = z.object({
	name: z.string().min(1, "Name is required"),
	email: z.string().email("Invalid email address"),
	password: z.string().min(8, "Password must be at least 8 characters"),
});
export type SignupFormValues = z.infer<typeof signupformSchema>;

export const forgotPasswordSchema = z.object({
	email: z.string().email("Invalid email address"),
});
export type ForgotPasswordFormValues = z.infer<typeof forgotPasswordSchema>;

export const resetPasswordSchema = z.object({
	token: z.string(),
	password: z.string().min(8, "Password must be at least 8 characters"),
});
export type ResetPasswordFormValues = z.infer<typeof resetPasswordSchema>;

export const verifyFormSchema = z.object({
	code: z.string().min(6, "Code must be 6 digits").max(6, "Code must be 6 digits"),
});
export type VerifyFormValues = z.infer<typeof verifyFormSchema>;