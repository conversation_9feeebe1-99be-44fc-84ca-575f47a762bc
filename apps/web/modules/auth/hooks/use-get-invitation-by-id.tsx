import { useQuery } from "convex/react";
import { api } from "@workspace/database/convex/_generated/api";
import type { Id } from "@workspace/database/convex/_generated/dataModel";

export function useGetInvitationById(invitationId: Id<"invitations">) {
  const invitation = useQuery(api.organizations.getInvitationById, {
    id: invitationId,
  });

  return {
    invitation,
    isLoading: invitation === undefined,
    error: invitation === null ? new Error("Failed to fetch invitation") : null,
  };
}
