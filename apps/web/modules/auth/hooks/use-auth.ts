import { authClient } from "@workspace/database/convex/auth/client";
import { config } from "@workspace/config";
import { useRouter } from "@/modules/shared/lib/router";
import { useQueryClient } from "@tanstack/react-query";
import { sessionQuery<PERSON>ey } from "@/modules/auth/lib/api";
import { useAuthErrorMessages } from "@/modules/shared/hooks/errors-messages";
import { useState } from "react";
import type { LoginFormValues, SignupFormValues, ForgotPasswordFormValues, ResetPasswordFormValues, VerifyFormValues } from "@/modules/auth/lib/validators";
import type { OAuthProvider } from "@/modules/auth/components/oauth-providers";

export function useAuth(redirectPath?: string) {
  const router = useRouter();
  const queryClient = useQueryClient();
  const { getAuthErrorMessage } = useAuthErrorMessages();
  const [isLoading, setIsLoading] = useState(false);

  const handleAuthError = (error: unknown) => {
    const errorMessage = getAuthErrorMessage(
      error && typeof error === "object" && "code" in error
        ? (error.code as string)
        : undefined
    );
    return { root: errorMessage };
  };

  const verifyTwoFactor = async (values: VerifyFormValues) => {
    setIsLoading(true);
    try {
      const { error } = await authClient.twoFactor.verifyTotp({
        code: values.code,
      });

      if (error) {
        throw error;
      }

      router.replace(redirectPath ?? config.auth.redirectAfterSignIn);
      return null;
    } catch (error) {
      return handleAuthError(error);
    } finally {
      setIsLoading(false);
    }
  };

  const resetPassword = async (values: ResetPasswordFormValues) => {
    setIsLoading(true);
    try {
      const { error } = await authClient.resetPassword({
        token: values.token,
        newPassword: values.password,
      });

      if (error) {
        throw error;
      }

      router.replace(config.auth.redirectAfterSignIn);
      return null;
    } catch (error) {
      return handleAuthError(error);
    } finally {
      setIsLoading(false);
    }
  };

  const forgotPassword = async (values: ForgotPasswordFormValues) => {
    setIsLoading(true);
    try {
      const redirectTo = new URL(
        "/auth/reset-password",
        window.location.origin,
      ).toString();

      const { error } = await authClient.forgetPassword({
        ...values,
        redirectTo,
      });

      if (error) {
        throw error;
      }

      return null;
    } catch (error) {
      return handleAuthError(error);
    } finally {
      setIsLoading(false);
    }
  };

  const signUpWithEmail = async (values: SignupFormValues) => {
    setIsLoading(true);
    try {
      const { error, data } = await authClient.signUp.email({
        ...values,
        onboardingComplete: false,
        role: "user",
      });

      if (error) {
        console.error("Signup error:", error);
        throw error;
      }

      console.log("Signup successful:", data);

      queryClient.invalidateQueries({
        queryKey: sessionQueryKey,
      });

      return null;
    } catch (error) {
      console.error("Signup error caught:", error);
      return handleAuthError(error);
    } finally {
      setIsLoading(false);
    }
  };

  const signInWithEmail = async (values: LoginFormValues) => {
    setIsLoading(true);
    try {
      if (values.mode === "password") {
        const { data, error } = await authClient.signIn.email({
          ...values,
        });

        if (error) {
          throw error;
        }

        if ((data as any).twoFactorRedirect) {
          router.replace("/auth/verify");
          return null;
        }

        queryClient.invalidateQueries({
          queryKey: sessionQueryKey,
        });

        router.replace(redirectPath ?? config.auth.redirectAfterSignIn);
        return null;
      } else {
        const { error } = await authClient.signIn.magicLink({
          ...values,
          callbackURL: redirectPath ?? config.auth.redirectAfterSignIn,
        });

        if (error) {
          throw error;
        }

        return null;
      }
    } catch (error) {
      return handleAuthError(error);
    } finally {
      setIsLoading(false);
    }
  };

  const signInWithPasskey = async () => {
    setIsLoading(true);
    try {
      await authClient.signIn.passkey();
      router.replace(redirectPath ?? config.auth.redirectAfterSignIn);
      return null;
    } catch (error) {
      return handleAuthError(error);
    } finally {
      setIsLoading(false);
    }
  };

  const signInWithOAuth = async (provider: OAuthProvider) => {
    setIsLoading(true);
    try {
      const callbackURL = new URL(redirectPath ?? config.auth.redirectAfterSignIn, window.location.origin);
      await authClient.signIn.social({
        provider,
        callbackURL: callbackURL.toString(),
      });
      return null;
    } catch (error) {
      return handleAuthError(error);
    } finally {
      setIsLoading(false);
    }
  };

  return {
    verifyTwoFactor,
    resetPassword,
    forgotPassword,
    signUpWithEmail,
    signInWithEmail,
    signInWithPasskey,
    signInWithOAuth,
    isLoading,
  };
}