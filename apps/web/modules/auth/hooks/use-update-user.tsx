import { useCallback, useMemo, useState } from "react";
import { useMutation } from "convex/react";
import { api } from "@workspace/database/convex/_generated/api";
import type { Id } from "@workspace/database/convex/_generated/dataModel";

type UpdateUserArgs = {
  id: Id<"users">;
  name?: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  image?: string;
};

type ResponseType = {
  message: string;
  status: string;
  data: any;
} | null;

type Options = {
  onSuccess?: (data: any) => void;
  onError?: (error: any) => void;
  onFinally?: () => void;
  throwError?: boolean;
};

export const useUpdateUser = () => {
  const [data, setData] = useState<ResponseType>(null);
  const [error, setError] = useState<Error | null>(null);
  const [status, setStatus] = useState<"loading" | "success" | "error" | "finally" | null>(null);

  const isLoading = useMemo(() => status === "loading", [status]);
  const isSuccess = useMemo(() => status === "success", [status]);
  const isError = useMemo(() => status === "error", [status]);
  const isFinally = useMemo(() => status === "finally", [status]);

  const mutation = useMutation(api.users.updateUser);

  const mutate = useCallback(async (values: UpdateUserArgs, options?: Options) => {
    try {
      setData(null);
      setStatus("loading");
      const response = await mutation(values);
      setData({
        message: "User updated successfully",
        status: "success",
        data: response
      });
      setStatus("success");
      options?.onSuccess?.(response);
    } catch (error) {
      setError(error as Error);
      setStatus("error");
      options?.onError?.(error);
      if (options?.throwError) {
        throw error;
      }
    } finally {
      setStatus("finally");
      options?.onFinally?.();
    }
  }, [mutation]);

  return {
    mutate,
    isLoading,
    isSuccess,
    isError,
    isFinally,
    data,
    error
  };
};
