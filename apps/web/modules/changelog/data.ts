import { type ChangelogItem } from "./types";

export const changelog: ChangelogItem[] = [
	{
		version: "1.0.0",
		date: "2025-07-19",
		title: "New SaaS template",
		description:
			"New SaaS template with a modern design and a focus on usability and accessibility.",
		type: ["feature", "improvement"],
		changes: [
			{
				type: "feature",
				description:
					"Comprehensive authentication system with organization support, invitation-only mode, and multiple auth strategies.",
			},
			{
				type: "feature",
				description:
					"AI integration framework with type-safe client implementation and customizable prompts system.",
			},
			{
				type: "feature",
				description:
					"Advanced email system with Resend integration and pre-built templates for auth flows.",
			},
			{
				type: "improvement",
				description:
					"Enhanced file storage system with EdgeStore provider and type-safe implementations.",
			},
			{
				type: "improvement",
				description:
					"Modern UI component library built with Shadcn UI, Radix, and Tailwind CSS.",
			},
			{
				type: "improvement",
				description:
					"Streamlined project structure with dedicated packages for AI, API, database, mail, and storage.",
			},
			{
				type: "improvement",
				description:
					"Type-safe API layer with OpenAPI schema generation and middleware support.",
			},
			{
				type: "improvement",
				description:
					"Comprehensive documentation system with MDX support and search capabilities.",
			}
		],
		content:
			"This release introduces major enhancements to the Start with Convex template, focusing on enterprise-ready features and developer experience. The authentication system now supports organization management, invitation-only mode, and multiple authentication strategies. We've added a powerful AI integration framework with type-safe implementations and a customizable prompts system. The email system has been enhanced with Resend integration and includes pre-built templates for various authentication flows. The UI layer has been modernized with a comprehensive component library built on Shadcn UI, Radix, and Tailwind CSS. The project structure has been optimized with dedicated packages for different concerns, making it easier to maintain and scale. The API layer is now fully type-safe with OpenAPI schema generation, and the documentation system has been improved with MDX support and search capabilities."
	}
];
