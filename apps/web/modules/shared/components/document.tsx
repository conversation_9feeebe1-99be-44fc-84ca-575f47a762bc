import { cookies } from "next/headers";
import type { PropsWithChildren } from "react";
import { ClientDocument } from "./client-document";

export async function Document({
	children,
}: PropsWithChildren) {
	const cookieStore = await cookies();
	const consentCookie = cookieStore.get("consent");

	return (
		<ClientDocument
			initialConsent={consentCookie?.value === "true"}
		>
			{children}
		</ClientDocument>
	);
}
