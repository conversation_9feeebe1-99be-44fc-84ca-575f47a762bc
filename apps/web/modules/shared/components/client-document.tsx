"use client";

import { AnalyticsScript, VemetricAnalyticsScript } from "@/modules/analytics";
import { ApiClientProvider } from "@/providers/api-client-provider";
import { ConsentBanner } from "@/modules/shared/components/consent-banner";
import { ConsentProvider } from "@/providers/consent-provider";
import { Toaster } from "sonner";
import { Provider as <PERSON><PERSON>Provider } from "jotai";
import { NuqsAdapter } from "nuqs/adapters/next/app";
import type { PropsWithChildren } from "react";

export function ClientDocument({
	children,
	initialConsent,
}: PropsWithChildren<{ initialConsent: boolean }>) {
	return (
		<NuqsAdapter>
			<ConsentProvider initialConsent={initialConsent}>
				<ApiClientProvider>
					<JotaiProvider>{children}</JotaiProvider>
				</ApiClientProvider>
				<Toaster position="top-right" />
				<ConsentBanner />
				<AnalyticsScript />
				<VemetricAnalyticsScript />
			</ConsentProvider>
		</NuqsAdapter>
	);
}
