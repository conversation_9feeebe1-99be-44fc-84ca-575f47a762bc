"use client";

import { useCookieConsent } from "@/hooks/use-cookie-consent";
import { Button } from "@workspace/ui/components/button";
import { Card, CardContent, CardFooter } from "@workspace/ui/components/card";
import { Checkbox } from "@workspace/ui/components/checkbox";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle,
} from "@workspace/ui/components/dialog";
import Cookies from "js-cookie";
import { CookieIcon } from "lucide-react";
import Link from "next/link";
import { useEffect, useState } from "react";

export function ConsentBanner() {
	const { userHasConsented, allowCookies, declineCookies } =
		useCookieConsent();
	const [mounted, setMounted] = useState(false);
	const [showPreferences, setShowPreferences] = useState(false);
	const [cookiePreferences, setCookiePreferences] = useState({
		necessary: true,
		analytic: false,
		marketing: false,
	});

	useEffect(() => {
		setMounted(true);
		if (typeof window !== "undefined") {
			const cookiePrefs = Cookies.get("cookieConsentPrefs");
			if (cookiePrefs) {
				try {
					const parsed = JSON.parse(cookiePrefs);
					if (typeof parsed === "object" && parsed !== null) {
						setCookiePreferences({
							necessary: true,
							analytic: !!parsed.analytic,
							marketing: !!parsed.marketing,
						});
						return;
					}
				} catch {}
			}
			// Fallback to localStorage
			const consent = localStorage.getItem("cookieConsent");
			if (consent) {
				try {
					const parsed = JSON.parse(consent);
					if (typeof parsed === "object" && parsed !== null) {
						setCookiePreferences({
							necessary: true,
							analytic: !!parsed.analytic,
							marketing: !!parsed.marketing,
						});
						// Sync to cookie for future
						Cookies.set("cookieConsentPrefs", consent, {
							expires: 30,
						});
						return;
					}
				} catch {
					if (consent === "all") {
						setCookiePreferences({
							necessary: true,
							analytic: true,
							marketing: true,
						});
						Cookies.set(
							"cookieConsentPrefs",
							JSON.stringify({
								necessary: true,
								analytic: true,
								marketing: true,
							}),
							{ expires: 30 },
						);
					} else {
						setCookiePreferences({
							necessary: true,
							analytic: false,
							marketing: false,
						});
						Cookies.set(
							"cookieConsentPrefs",
							JSON.stringify({
								necessary: true,
								analytic: false,
								marketing: false,
							}),
							{ expires: 30 },
						);
					}
				}
			}
		}
	}, []);

	const setPrefsEverywhere = (prefs: typeof cookiePreferences) => {
		const json = JSON.stringify(prefs);
		localStorage.setItem("cookieConsent", json);
		Cookies.set("cookieConsentPrefs", json, { expires: 30 });
	};

	const acceptAllCookies = () => {
		const prefs = { necessary: true, analytic: true, marketing: true };
		setPrefsEverywhere(prefs);
		setCookiePreferences(prefs);
		allowCookies();
	};

	const rejectAllCookies = () => {
		const prefs = { necessary: true, analytic: false, marketing: false };
		setPrefsEverywhere(prefs);
		setCookiePreferences(prefs);
		declineCookies();
	};

	const savePreferences = () => {
		setPrefsEverywhere(cookiePreferences);
		if (cookiePreferences.analytic || cookiePreferences.marketing) {
			allowCookies();
		} else {
			declineCookies();
		}
		setShowPreferences(false);
	};

	const handlePreferenceChange = (type: "analytic" | "marketing") => {
		setCookiePreferences((prev) => ({ ...prev, [type]: !prev[type] }));
	};

	if (!mounted || userHasConsented) {
		return null;
	}

	return (
		<>
			<div className="fixed left-4 bottom-4 max-w-md z-50">
				<Card className="!p-0 !gap-0">
					<CardContent className="p-4 flex gap-4">
						<CookieIcon className="block size-6 shrink-0 text-5xl text-primary/60 mt-1" />
						<div>
							<h2 className="text-lg font-bold mb-1">
								Cookies & Privacy
							</h2>
							<p className="text-sm">
								We use cookies to improve your experience. You
								can opt out of certain cookies. Find out more in
								our{" "}
								<Link
									href="/legal/privacy-policy"
									className="text-primary underline hover:opacity-80"
								>
									privacy policy
								</Link>
								.
							</p>
						</div>
					</CardContent>
					<CardFooter className="flex justify-end space-x-2 p-4">
						<Button
							variant="link"
							onClick={() => setShowPreferences(true)}
						>
							Preferences
						</Button>
						<Button variant="outline" onClick={rejectAllCookies}>
							Reject all
						</Button>
						<Button variant="primary" onClick={acceptAllCookies}>
							Accept all
						</Button>
					</CardFooter>
				</Card>
			</div>

			<Dialog open={showPreferences} onOpenChange={setShowPreferences}>
				<DialogContent>
					<DialogHeader>
						<DialogTitle>Cookie preferences</DialogTitle>
						<DialogDescription>
							By clicking on "Accept all" you are giving us
							permission to collect and use all types of cookies
							on our website or you can select only some types
							based on your preferences. You can change your
							decision at any time or opt-out: find out more
							information in our{" "}
							<Link
								href="/legal/privacy-policy"
								className="text-primary underline hover:opacity-80"
							>
								Privacy Policy
							</Link>
							.
						</DialogDescription>
					</DialogHeader>
					<div className="space-y-4">
						<div className="flex items-center space-x-2">
							<Checkbox id="necessary" checked disabled />
							<label
								htmlFor="necessary"
								className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-gray-700"
							>
								Necessary cookies
							</label>
						</div>
						<p className="text-sm text-gray-500">
							Necessary cookies help make a website usable by
							enabling basic functions like page navigation and
							access to secure areas of the website. The website
							cannot function properly without these cookies.
						</p>

						<div className="flex items-center space-x-2">
							<Checkbox
								id="analytic"
								checked={cookiePreferences.analytic}
								onCheckedChange={() =>
									handlePreferenceChange("analytic")
								}
							/>
							<label
								htmlFor="analytic"
								className="text-sm font-medium leading-none text-gray-700"
							>
								Analytic cookies
							</label>
						</div>
						<p className="text-sm text-gray-500">
							Used for measuring and analytics of the website's
							traffic and collection of statistical data about the
							use of the website. The main goal is to make the
							website as user-friendly as possible.
						</p>

						<div className="flex items-center space-x-2">
							<Checkbox
								id="marketing"
								checked={cookiePreferences.marketing}
								onCheckedChange={() =>
									handlePreferenceChange("marketing")
								}
							/>
							<label
								htmlFor="marketing"
								className="text-sm font-medium leading-none text-gray-700"
							>
								Marketing and advertising cookies
							</label>
						</div>
						<p className="text-sm text-gray-500">
							Used for personalizing, optimizing and customizing
							of content and ads.
						</p>
					</div>
					<div className="flex justify-end space-x-2 mt-4">
						<Button
							variant="outline"
							onClick={() => setShowPreferences(false)}
							className="text-gray-700"
						>
							Cancel
						</Button>
						<Button onClick={savePreferences}>
							Save preferences
						</Button>
					</div>
				</DialogContent>
			</Dialog>
		</>
	);
}
