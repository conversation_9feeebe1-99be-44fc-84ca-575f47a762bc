import { useQuery } from "convex/react";
import { api } from "@workspace/database/convex/_generated/api";
import type { Id } from "@workspace/database/convex/_generated/dataModel";

export function useGetUserAuthMethods(userId: Id<"users"> | null) {
  if (!userId) {
    return {
      authMethods: null,
      isLoading: false,
      error: new Error("User ID is required"),
    };
  }

  const authMethods = useQuery(api.users.getUserAuthMethods, {
    userId,
  });

  return {
    authMethods,
    isLoading: authMethods === undefined,
    error: authMethods === null ? new Error("Failed to fetch auth methods") : null,
  };
}
