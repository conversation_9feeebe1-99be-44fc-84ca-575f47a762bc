"use client";

import { useEffect } from "react";
import { vemetric } from "@vemetric/web";

const vemetricToken = process.env.NEXT_PUBLIC_VEMETRIC_ID as string;

export function VemetricAnalyticsScript() {
	useEffect(() => {
		if (!vemetricToken) return;
		vemetric.init({
			token: vemetricToken,
		});
	}, []);
	return null;
}

export function useVemetricAnalytics() {
	function trackEvent(event: string, data?: Record<string, unknown>) {
		if (!vemetricToken) return;
		vemetric.trackEvent(event, {
			eventData: data,
		});
	}

	return {
		trackEvent,
	};
}
