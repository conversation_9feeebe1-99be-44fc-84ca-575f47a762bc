import { PostListItem } from "@/modules/blog/components/post-list-item";
import { getAllPosts } from "@/modules/blog/utils/lib/posts";

export async function generateMetadata() {
	return {
		title: "Blog",
	};
}

export default async function BlogListPage() {
	const posts = await getAllPosts();

	return (
		<div className="container max-w-6xl">
			<div className="mb-12 pt-8 text-center">
				<h1 className="mb-2 font-bold text-5xl">Blog</h1>
				<p className="text-lg opacity-50">
					What&apos;s new in the workspace?
				</p>
			</div>

			<div className="grid gap-8 md:grid-cols-2">
				{posts
					.filter((post) => post.published)
					.sort(
						(a, b) =>
							new Date(b.date).getTime() -
							new Date(a.date).getTime(),
					)
					.map((post) => (
						<PostListItem 
							post={post} 
							key={`${post.path}`} 
						/>
					))}
			</div>
		</div>
	);
}
