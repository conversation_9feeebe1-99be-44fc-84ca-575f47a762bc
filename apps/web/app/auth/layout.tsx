import { SessionProvider } from "@/providers/session-provider";
import { AuthWrapper } from "@/modules/auth/components/auth-wrapper";
import type { PropsWithChildren } from "react";
import { Document } from "@/modules/shared/components/document";

export const dynamic = "force-dynamic";
export const revalidate = 0;

export default function AuthLayout({ children }: PropsWithChildren) {
	return (
    <Document>
      <SessionProvider>
        <AuthWrapper contentClass="min-w-sm max-w-sm">{children}</AuthWrapper>
      </SessionProvider>
    </Document>
	);
}
