import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono, <PERSON> } from "next/font/google"

import "@workspace/ui/globals.css"
import { Providers } from "../components/providers"
import { ConvexClientProvider } from "../providers/convex-provider";
import Header from "../components/header";

const fontSans = Geist({
  subsets: ["latin"],
  variable: "--font-sans",
})

const fontMono = Geist_Mono({
  subsets: ["latin"],
  variable: "--font-mono",
})

const fontInter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
})

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${fontSans.variable} ${fontMono.variable} ${fontInter.variable} font-sans antialiased max-h-screen min-h-screen overflow-hidden`}
      >
        <Providers>
          <ConvexClientProvider>
            <Header />
              {children}
          </ConvexClientProvider>
        </Providers>
      </body>
    </html>
  )
}
