import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";

// Better Auth handles authentication through Convex HTTP routes
// No middleware needed for Better Auth + Convex setup
export function middleware(request: NextRequest) {
  return NextResponse.next();
}

export const config = {
  // The following matcher runs middleware on all routes
  // except static assets.
  matcher: ["/((?!.*\\..*|_next).*)", "/", "/(api|trpc)(.*)"],
};