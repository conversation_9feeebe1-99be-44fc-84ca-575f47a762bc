import { config } from "@workspace/config";
import { createI18nSearchAPI } from "fumadocs-core/search/server";
import { docsSource } from "@/app/docs-source";

export const { GET } = createI18nSearchAPI("advanced", {
	i18n: {
		defaultLanguage: "en",
		languages: ["en"],
	},
	indexes: docsSource.getLanguages().flatMap((entry: any) =>
		entry.pages.map((page: any) => ({
			title: page.data.title,
			description: page.data.description,
			structuredData: page.data.structuredData,
			id: page.url,
			url: page.url,
		})),
	),
});
