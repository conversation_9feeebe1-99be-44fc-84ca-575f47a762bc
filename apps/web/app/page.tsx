"use client";

import HeroS<PERSON><PERSON> from "@/components/hero-section"
import { ConvexNudge } from '@convex-nudge/react'
import { useIsMobile } from "@workspace/ui/hooks/use-mobile"

export default function Page() {
  const mobile = useIsMobile()
  return (
    <>
      <HeroSection />
      <ConvexNudge
        variant="dark"
        position={mobile ? 'bottom' : 'left'}
        animation="slide"
        textSize={mobile ? 'xs' : 'base'}
        logoSize={mobile ? 24 : 32}
        referralCode="startwithconvex"
        className={`${mobile ? 'hidden' : '-ml-30'} font-mono lowercase`}
        fixed
      />
    </>
  )
}
