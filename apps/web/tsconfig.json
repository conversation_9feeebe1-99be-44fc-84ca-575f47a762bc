{"extends": "@workspace/typescript-config/nextjs.json", "compilerOptions": {"baseUrl": ".", "paths": {"@/*": ["./*"], "@workspace/ui/*": ["../../packages/ui/src/*"], "content-collections": ["./.content-collections/generated"]}, "plugins": [{"name": "next"}]}, "include": ["next-env.d.ts", "next.config.ts", "**/*.ts", "**/*.tsx", "**/*.cjs", "**/*.mjs", ".next/types/**/*.ts"], "exclude": ["node_modules"]}