import { getAllPosts } from "@/modules/blog/utils/lib/posts";
import { config } from "@workspace/config";
import { getBaseUrl } from "@workspace/utils";
import { allLegalPages } from "content-collections";
import type { MetadataRoute } from "next";
import { docsSource } from "@/app/docs-source";

const baseUrl = getBaseUrl();

const staticMarketingPages = ["", "/changelog"];

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
	const posts = await getAllPosts();

	return [
		...staticMarketingPages.flatMap((page) =>
			["en"].map((locale) => ({
				url: new URL(`/${locale}${page}`, baseUrl).href,
				lastModified: new Date(),
			})),
		),
		...posts.map((post) => ({
			url: new URL(`/blog/${post.path}`, baseUrl).href,
			lastModified: new Date(),
		})),
		...allLegalPages.map((page) => ({
			url: new URL(`/legal/${page.path}`, baseUrl).href,
			lastModified: new Date(),
		})),
		...docsSource.getPages().map((page) => ({
			url: new URL(`/docs/${page.slugs.join("/")}`, baseUrl).href,
			lastModified: new Date(),
		})),
	];
}
