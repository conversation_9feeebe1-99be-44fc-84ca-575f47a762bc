import "server-only";

import { auth } from "@workspace/database/convex/auth"
import { headers } from "next/headers";
import { cache } from "react";
import { api } from "@workspace/database/convex/_generated/api";
import { convex } from "@workspace/database/convex/helpers/client";
import type { Id } from "@workspace/database/convex/_generated/dataModel";

export const getSession = cache(async () => {
	const session = await auth.api.getSession({
		headers: await headers(),
		query: {
			disableCookieCache: true,
		},
	});

	return session;
});

export const getActiveOrganization = cache(async (slug: string) => {
	try {
		const activeOrganization = await (auth as any).api.getFullOrganization({
			query: {
				organizationSlug: slug,
			},
			headers: await headers(),
		});

		return activeOrganization;
	} catch (error) {
		return null;
	}
});

export const getOrganizationList = cache(async () => {
	try {
		const organizationList = await (auth as any).api.listOrganizations({
			headers: await headers(),
		});

		return organizationList;
	} catch (error) {
		return [];
	}
});

export const getUserAccounts = cache(async () => {
	try {
		const userAccounts = await (auth as any).api.listUserAccounts({
			headers: await headers(),
		});

		return userAccounts;
	} catch (error) {
		return [];
	}
});

export const getUserPasskeys = cache(async () => {
	try {
		const userPasskeys = await (auth as any).api.listPasskeys({
			headers: await headers(),
		});

		return userPasskeys;
	} catch (error) {
		return [];
	}
});

export const getInvitation = cache(async (id: string) => {
	try {
		return await (convex as any).query(api.organizations.getInvitationById, {
			id: id as Id<"invitations">,
		});
	} catch (error) {
		return null;
	}
});
