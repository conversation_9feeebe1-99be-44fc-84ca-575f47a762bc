import "server-only";

import { cache } from "react";
import { api } from "@workspace/database/convex/_generated/api";
import { convex } from "@workspace/database/convex/helpers/client";
import type { Id } from "@workspace/database/convex/_generated/dataModel";
import { createAuth } from "@workspace/database/lib/auth";
import { getToken } from "@convex-dev/better-auth/nextjs";
import { fetchQuery } from "convex/nextjs";

export const getSession = cache(async () => {
	try {
		const token = await getToken(createAuth);
		if (!token) {
			return null;
		}

		const session = await convex.query(api.auth.getCurrentUser, {}, { token });
		return session;
	} catch (error) {
		return null;
	}
});

export const getActiveOrganization = cache(async (_slug: string) => {
	try {
		// For now, return null - these functions need to be implemented
		// using Convex queries instead of auth.api methods
		return null;
	} catch (error) {
		return null;
	}
});

export const getOrganizationList = cache(async () => {
	try {
		// For now, return empty array - these functions need to be implemented
		// using Convex queries instead of auth.api methods
		return [];
	} catch (error) {
		return [];
	}
});

export const getUserAccounts = cache(async () => {
	try {
		// For now, return empty array - these functions need to be implemented
		// using Convex queries instead of auth.api methods
		return [];
	} catch (error) {
		return [];
	}
});

export const getUserPasskeys = cache(async () => {
	try {
		// For now, return empty array - these functions need to be implemented
		// using Convex queries instead of auth.api methods
		return [];
	} catch (error) {
		return [];
	}
});

export const getInvitation = cache(async (id: string) => {
	try {
		return await convex.query(api.organizations.getInvitationById, {
			id: id as Id<"invitations">,
		});
	} catch (error) {
		return null;
	}
});
